#!/bin/bash
# 这个脚本用于编译各种平台一键构建脚本
# 支持Linux-x86_64、android aarch64 、android aarch32


function  build_Linux_x86_64(){
    echo "build_Linux_x86_64"
    rm -rf build_linux_x86_64
    mkdir build_linux_x86_64
    cd build_linux_x86_64

    cmake  -DCMAKE_BUILD_TYPE=Release -DLinux-x86_64=ON -DUSE_MNN=ON -DHTTP_INFERENCE=ON -DCOMPILER_NAME=Linux-x86_64   -DMODEL_TEST_MODE=OFF -DINTERNAL_TEST_TOOL_MODE=OFF -DCAR_BUILD_TYPE=BYD_HA6\
    ..

    make -j20
    cd ..
}

function  build_Android_aarch64(){
    echo "build_Android_aarch64"

    export ANDROID_NDK="/opt/android-ndk-r23b"

    rm -rf build_android_aarch64
    mkdir build_android_aarch64
    cd build_android_aarch64

    cmake -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK/build/cmake/android.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release -DAndroid_r18b=ON -DUSE_MNN=ON   -DMODEL_TEST_MODE=OFF -DINTERNAL_TEST_TOOL_MODE=OFF -DCAR_BUILD_TYPE=BYD_HA6 \
    -DCOMPILER_NAME=Android_r18b -DANDROID_STL=c++_static -DANDROID_PLATFORM=android-31 \
    -DANDROID_ABI=arm64-v8a  -DCMAKE_CXX_FLAGS='-std=c++11  -frtti -fexceptions ' ..

    make -j4
    cd ..
}

function  build_Android_aarch32(){
    echo "build_Android_aarch32"

    export ANDROID_NDK="/opt/android-ndk-r17c"

    rm -rf build_android_aarch32
    mkdir build_android_aarch32
    cd build_android_aarch32

    cmake -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK/build/cmake/android.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release -DAndroid_r18b=ON -DUSE_MNN=ON \
    -DCOMPILER_NAME=Android_r18b -DANDROID_STL=c++_static -DANDROID_PLATFORM=android-17 \
    -DANDROID_ABI=armeabi-v7a  -DCMAKE_CXX_FLAGS='-std=c++11  -frtti -fexceptions' ..

    make -j4
    cd ..
}
function  build_Linux_amba_aarch64(){
    echo "build_Linux_amba_aarch64"
    rm -rf build_linux_amba_aarch64
    mkdir build_linux_amba_aarch64
    cd build_linux_amba_aarch64

    PARENT_DIR=$(cd $(dirname $0);cd ..; pwd) #上层路径

    cmake  -DCMAKE_TOOLCHAIN_FILE=${PARENT_DIR}/toolchains/linux_amba_aarch64.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release -DUSE_AMBA=ON  -DUSE_MNN=ON  -DCOMPILER_NAME=Linux_amba_aarch64 \
    -DCMAKE_CXX_FLAGS='-DAMBA_SDK' ..

    make -j4
    cd ..
}
function  build_Linux_th518(){
    echo "build_Linux_th518"

    rm -rf build_linux_th518
    mkdir build_linux_th518
    cd build_linux_th518

    # CURRENT_DIR=$(cd $(dirname $0); pwd)  当前路径
    PARENT_DIR=$(cd $(dirname $0);cd ..; pwd) #上层路径

    echo $PARENT_DIR

    cmake  -DCMAKE_TOOLCHAIN_FILE=${PARENT_DIR}/toolchains/jingshi-cv1838-aarch64-arm.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release -DLinux_TH518=ON  -DUSE_MNN=ON  -DCOMPILER_NAME=Linux_aarch64  ..

    make -j4
    cd ..
}


function  build_Linux_ov_aarch32(){
    echo "build_Linux_ov_aarch32"

    rm -rf build_Linux_ov_aarch32
    mkdir build_Linux_ov_aarch32
    cd build_Linux_ov_aarch32

    # CURRENT_DIR=$(cd $(dirname $0); pwd)  当前路径
    PARENT_DIR=$(cd $(dirname $0);cd ..; pwd) #上层路径

    echo $PARENT_DIR

    cmake  -DCMAKE_TOOLCHAIN_FILE=../toolchains/linux_ov_linux_uclib.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Debug -DLinux_aarch32=ON  -DUSE_MNN=ON  -DCOMPILER_NAME=Linux_ov_aarch32 \
    -DONLY_LANDMARKS=ON -DCMAKE_CXX_FLAGS='-DONLY_LANDMARKS' ..

    make -j4
    cd ..
}

function  build_Android_lc_ddaw(){
    echo "build_Android_lc_ddaw"

    rm -rf build_Android_lc_ddaw
    mkdir build_Android_lc_ddaw
    cd build_Android_lc_ddaw

    # CURRENT_DIR=$(cd $(dirname $0); pwd)  当前路径
    PARENT_DIR=$(cd $(dirname $0);cd ..; pwd) #上层路径

    echo $PARENT_DIR

    cmake -DCMAKE_TOOLCHAIN_FILE=${PARENT_DIR}/toolchains/xinchi-x9h-arm.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Debug \
    -DANDROID_ABI=arm64-v8a \
    -DANDROID_STL=c++_shared \
    -DANDROID_NATIVE_API_LEVEL=31 \
    -DANDROID_TOOLCHAIN=clang++ ..

    make -j12
    cd ..
}

function  build_Linux_oax4600_aarch64(){
    echo "build_Linux_oax4600-aarch64"
    rm build_Linux_oax4600-aarch64/ -rf
    mkdir -p build_Linux_oax4600-aarch64/
    cd build_Linux_oax4600-aarch64/
    PARENT_DIR=$(cd $(dirname $0);cd ..; pwd) #上层路径

    echo $PARENT_DIR

    cmake  -DCMAKE_TOOLCHAIN_FILE=../toolchains/linux_oax4600_aarch64.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release  -DCOMPILER_NAME=Linux_aarch64 -DHTTP_INFERENCE=OFF  -DUSE_ACTIVATE=ON -DCAR_BUILD_TYPE=BYD_HA6 -DMODEL_TEST_MODE=OFF -DINTERNAL_TEST_TOOL_MODE=OFF   -DCMAKE_CXX_FLAGS=' -fPIC ' -DCMAKE_C_FLAGS=' -fPIC '  ..

    make -j10
    cd ..
}

##平台数组
Plant_array=(
linux 
android
)


#芯片架构数组
Architecture_array=(
x86_64
aarch64
ov_aarch32
th518
amba_aarch64
lc_ddaoax4600
)

# $1 如果存在，输出 $1 exists，$1 如果不存在，输出 $1 not exists
if [[ "$1" != "" && "$2" != "" ]];then
    if [[ "${Plant_array[@]}"  =~ "${1}" && "${Architecture_array[@]}"  =~ "${2}" ]]; then
        echo "$1  $2 exists"
        if [[ "${1}" == "linux" && "${2}" == "x86_64"  ]]; then
            build_Linux_x86_64
        elif [[ "${1}" == "android" && "${2}" == "aarch64"  ]]; then
            build_Android_aarch64
        elif [[ "${1}" == "android" && "${2}" == "aarch32"  ]]; then
            build_Android_aarch32
        elif [[ "${1}" == "linux" && "${2}" == "ov_aarch32"  ]]; then
            build_Linux_ov_aarch32
        elif [[ "${1}" == "linux" && "${2}" == "amba_aarch64"  ]]; then
            build_Linux_amba_aarch64
        elif [[ "${1}" == "linux" && "${2}" == "oax4600"  ]]; then
            build_Linux_oax4600_aarch64
	elif [[ "${1}" == "android" && "${2}" == "lc_ddaw"  ]]; then
            build_Android_lc_ddaw
        else
            echo "不支持的平台架构选项！"
        fi

    elif [[ ! "${Plant_array[@]}"  =~ "${1}"  ||  ! "${Architecture_array[@]}"  =~ "${2}" ]]; then
        echo "$1 $2 not exists"
    fi
else
    echo "请传入 平台类型 芯片架构/项目名称 两个参数"
    echo "编译例子如下："
    echo "build.sh 平台类型 芯片架构/项目名称"
    echo "build.sh linux x86_64"
    echo "build.sh linux th518"
    echo "build.sh android aarch64"
    echo "build.sh android aarch32"
    echo "build.sh linux ov_aarch32"
    echo "build.sh linux amba_aarch64"
    echo "build.sh android lc_ddaw"
    echo "build.sh linux oax4600"
fi

