#include "activate.h"
#include <dirent.h>
#include <net/if.h>
#include <netinet/in.h>
#include <stdio.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
// #include "CHttpClient.hpp"
// #include "json.h"
#include "md5.h"

/**比亚迪4600海外激活代码 */
ActivateService& ActivateService::instance() {
    static ActivateService instance_;
    return instance_;
}
bool ActivateService::GetActivateStatus() {
    return flag_activate;
}
ActivateService::ActivateService() {
    m_uuid = "";
}

ActivateService::~ActivateService() {}

static int getoax4600id(std::string& ov4600_str) {
    const char* filePath = "/proc/uid";
    std::ifstream file(filePath);

    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filePath << std::endl;
        return -1;
    }
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    ov4600_str = content;
    // std::cout << "uuid: " << ov4600_str << std::endl;

    return 0;
}

//校验激活码
int verifiActivationCode(std::string did, std::string proid, std::string code_str) {
    // key是固定的，对应于特定的算法
    // 1. adas算法：96k018515hk；
    // 2. 脱敏算法：96k018522hk;
    // 3. dms算法：96k018516hk;
    std::string data_key = "96k018516hk";

    std::string uuid_str = data_key + did;
    std::string proid_str = "r7" + proid;
    // std::cout<< "uuid_str:" << uuid_str <<std::endl;
    std::string md5_result = MD5(uuid_str).toStr();
    // std::cout<< "md5_result:" << md5_result <<std::endl;
    std::string result = MD5(md5_result + proid_str).toStr();

    // std::cout<< "result:" << result <<std::endl;
    // std::cout<< "code_str:" << code_str <<std::endl<<std::endl;
    int ret = result.compare(code_str);
    return ret;
}

int ActivateService::run(const std::string activate_code, const int activate_code_len) {
    int status = 0;  //默认成功

    // std::string uuid = m_uuid;
    std::string uuid = "";
    getoax4600id(uuid);  //自动获取flash ID
    if (uuid == "") {
        std::cout << "get uuid failed!" << std::endl;
        status = 100;
        return status;  // uuid获取失败
    }
    std::cout << "uuid: " << uuid << std::endl;

    std::string activate_code_str = activate_code;
    for (char& c : activate_code_str) {
        c = std::tolower(c);  // 将每个字符转换为小写
    }

    std::string proid = "byd";
    int ret = verifiActivationCode(uuid, proid, activate_code_str);

    if (ret == 0) {
        flag_activate = true;
    } else {
        status = 200;
    }
    return status;
}

// 设置uuid
void ActivateService::setuuid(const std::string& uuid_str) {
    m_uuid = uuid_str;
}
