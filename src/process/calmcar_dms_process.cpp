#include "calmcar_dms_process.h"
#include <string.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <cstdio>
#include <deque>
#include <fstream>
#include <numeric>
#include <string>
#include "CalmCarLog.h"
#include "android_log.h"
#include "cc_numarray_tool.h"
#include "cc_resource_register.h"
#include "cc_version.h"
#include "json.h"
#include "opencv2/opencv.hpp"
#include "tx_media_server_interface.h"

#ifdef AMBA_SDK
#include "eazyai.h"
#endif
#include <limits>
namespace tongxing {

// 关键点部位映射定义
const std::map<FacialRegion, std::vector<int>> CcDmsProcess::REGION_KEYPOINTS = {
    {FacialRegion::LEFT_EYE, {0, 1, 12, 13}},      // 左眼4个关键点
    {FacialRegion::RIGHT_EYE, {2, 3, 14, 15}},     // 右眼4个关键点
    {FacialRegion::NOSE, {4, 9, 10, 11}},          // 鼻子4个关键点
    {FacialRegion::MOUTH, {5, 6, 7, 8}},           // 嘴巴4个关键点
    {FacialRegion::NOSE_BRIDGE, {19, 20, 21}},     // 鼻梁3个关键点
    {FacialRegion::OTHER_POINTS, {16, 17, 18}}     // 其他关键点
};

// 部位名称映射定义
const std::map<FacialRegion, std::string> CcDmsProcess::REGION_NAMES = {
    {FacialRegion::LEFT_EYE, "LEFT_EYE"},
    {FacialRegion::RIGHT_EYE, "RIGHT_EYE"},
    {FacialRegion::NOSE, "NOSE"},
    {FacialRegion::MOUTH, "MOUTH"},
    {FacialRegion::NOSE_BRIDGE, "NOSE_BRIDGE"},
    {FacialRegion::OTHER_POINTS, "OTHER_POINTS"}
};

// 眼部区域关键点映射定义
const std::map<EyeRegion, std::vector<int>> CcDmsProcess::EYE_REGION_KEYPOINTS = {
    {EyeRegion::EYE_CONTOUR, {0, 1, 2, 3, 4, 5, 6, 7}},      // 眼睛轮廓8个关键点
    {EyeRegion::IRIS_CONTOUR, {8, 9, 10, 11, 12, 13, 14, 15}}, // 虹膜轮廓8个关键点
    {EyeRegion::PUPIL, {16}}                                   // 瞳孔1个关键点
};

// 眼部区域名称映射定义
const std::map<EyeRegion, std::string> CcDmsProcess::EYE_REGION_NAMES = {
    {EyeRegion::EYE_CONTOUR, "EYE_CONTOUR"},
    {EyeRegion::IRIS_CONTOUR, "IRIS_CONTOUR"},
    {EyeRegion::PUPIL, "PUPIL"}
};

static std::string getCurrentTimes() {
    struct timeval tv;
    gettimeofday(&tv, NULL);

    static const int MAX_BUFFER_SIZE = 128;
    char timestamp_str[MAX_BUFFER_SIZE];
    time_t sec = static_cast<time_t>(tv.tv_sec);
    int ms = static_cast<int>(tv.tv_usec) / 1000;

    struct tm tm_time;
    localtime_r(&sec, &tm_time);
    static const char* formater = "%4d-%02d-%02d %02d:%02d:%02d.%03d";
    int wsize = snprintf(timestamp_str, MAX_BUFFER_SIZE, formater, tm_time.tm_year + 1900,
                         tm_time.tm_mon + 1, tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min,
                         tm_time.tm_sec, ms);

    timestamp_str[std::min(wsize, MAX_BUFFER_SIZE - 1)] = '\0';
    return std::string(timestamp_str);
}
static void parsePixelValue(const std::shared_ptr<NumArray>& pixels_histogram, int& pixel_peak) {
    uint64_t* hist_data = (uint64_t*)pixels_histogram->data;
    int max_value = hist_data[0];
    pixel_peak = 0;
    int total_pixels = hist_data[0];

    for (int i = 1; i < 256; ++i) {
        int pixel_value = hist_data[i];

        if (pixel_value > max_value) {
            max_value = pixel_value;
            pixel_peak = i;
        }
        total_pixels += pixel_value;
    }
    // std::cout << "total_pixels:" << total_pixels << std::endl;
    // std::cout << "Pixel peak found at index: " << pixel_peak
    //           << " with value: " << max_value << std::endl;
}

#if ANDROID
void androidLog(char* data, int data_size) {
    DLOGE("%s", data);
}
#endif
int CcDmsProcess::Init(const char* config_file, const char* cache_path) {
    // CalmCarLog::getInstance()->set_calmcar_log_type(FILE_OUTPUT, "/mnt/sdcard/", "calmcar_dms_log_", 20000, NULL);
    // CalmCarLog::getInstance()->set_calmcar_log_type(FILE_OUTPUT, "./", "calmcar_n50_dms_log_", 2000, NULL);

#if ANDROID
    CalmCarLog::getInstance()->set_calmcar_log_type(REDIRECT_OUTPUT, nullptr, nullptr, 0,
                                                    androidLog);
    CalmCarLog::getInstance()->set_calmcar_log_level(INFO);
#else
    CalmCarLog::getInstance()->set_calmcar_log_level(INFO);
#endif
    frame_id = 0;
    if (cache_path) {
        cache_path_ = std::string(cache_path);
    }

    Json::Reader model_config_json_reader;
    Json::Value model_config_root;
    auto model_config_json_data = CcResourcDataRegister::instance().get_function("dms_config.json");
    // assert(config_json_data!=NULL);
    // std::cout<<(const char*)(config_json_data.second)<<std::endl;
    std::string model_config_doc =
        std::string((char*)model_config_json_data.second, model_config_json_data.first);
    if (!model_config_json_reader.parse(model_config_doc, model_config_root)) {
        TX_LOG_FATAL("TX DMS", "Parse json config file failed!");
        return -1;
    }
    grop = tongxing::get_cc_module(model_config_root);
    dms_process_.Init(std::bind(&CcDmsProcess::get_driving_face_bbox, this),
                      std::bind(&CcDmsProcess::get_driving_face_keypoint, this),
                      std::bind(&CcDmsProcess::get_driving_face_angle, this),
                      std::bind(&CcDmsProcess::get_driving_eye_keypoint, this),
                      std::bind(&CcDmsProcess::get_driving_right_eye_close_score, this),
                      std::bind(&CcDmsProcess::get_driving_left_eye_close_score, this),
                      std::bind(&CcDmsProcess::get_occlusion_status, this),
                      std::bind(&CcDmsProcess::get_driving_face_attr, this),
                      std::bind(&CcDmsProcess::get_driving_right_eye_landmarks, this),
                      std::bind(&CcDmsProcess::get_driving_left_eye_landmarks, this),
                      std::bind(&CcDmsProcess::get_exact_lum_info, this),
                      std::bind(&CcDmsProcess::get_phone_cig_bbox, this),
                      std::bind(&CcDmsProcess::get_keypoints_stability_analysis, this), config_file);

    r_eyeinfo = {0};
    l_eyeinfo = {0};

    leye_lcorner_pos_queue.clear();
    leye_rcorner_pos_queue.clear();
    lheadangle_queue.clear();

    reye_lcorner_pos_queue.clear();
    reye_rcorner_pos_queue.clear();
    rheadangle_queue.clear();
    //设置驾驶员默认区域
    TXPoint2i left_top_point;
    TXPoint2i right_bottom_point;
//TODO:将face roi的配置设置成项目配置文件相关，所以需要解耦出来parsedata这部分模块
#if defined(BYD_SC3E) ||                                                       \
    defined(BYD_HA6) || defined(BYD_SC3E_R)  // 现在的face roi设置在当前图像输入为1280*800的基础上
    left_top_point.x = 230;
    left_top_point.y = 0;
    right_bottom_point.x = 1280;
    right_bottom_point.y = 800;
#elif defined(BYD_EQ_R)
    left_top_point.x = 0;
    left_top_point.y = 0;
    right_bottom_point.x = 1050;
    right_bottom_point.y = 800;
#elif defined(BYD_EQ)
    left_top_point.x = 80;
    left_top_point.y = 0;
    right_bottom_point.x = 1280;
    right_bottom_point.y = 800;
#endif
    driver_roi_left_top_point = left_top_point;
    driver_roi_right_bottom_point = right_bottom_point;

#if defined(BYD_SC3E)
    build_type_ = "BYD_SC3E";
#elif defined(BYD_EQ)
    build_type_ = "BYD_EQ";
#elif defined(BYD_EQ_R)
    build_type_ = "BYD_EQ_R";
#elif defined(BYD_HA6)
    build_type_ = "BYD_HA6";
#elif defined(BYD_SC3E_R)
    build_type_ = "BYD_SC3E_R";
#else
    build_type_ = "UNKNOWN";
#endif

    return 0;
}

int CcDmsProcess::GetVersion(std::string& version) {
    version = dms_get_version();
    TX_LOG_INFO("process", "ver : %s", version.c_str());
    return 0;
}

// bool flag_e=false;
// char v_data[1600*1300];
// long id = 0;
int CcDmsProcess::SetInput(const TXImageInfo* image, TXDmsResult* result) {
    // auto start_time = std::chrono::system_clock::now();
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long start_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    std::shared_ptr<NumArray> image_numArray(new NumArray());
    image_numArray->type = tongxing::NumArray::UINT8;
    image_numArray->word_size = 1;
    image_numArray->data = (unsigned char*)image->data;
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(image->height);
    image_numArray->shape.push_back(image->width);

    int iRet = once_process(image_numArray, result);

    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "当前帧算法所耗时间为：" << cost_time.count() << "ms" << std::endl;
#if 0
    // 写入各种类型的数据到stringstream
    // auto starttime = std::chrono::system_clock::now();
    std::stringstream ss;
    ss << getCurrentTimes();
    ss << "[ABCD]{";
    ss << "\"result_frame_id\":" << result->result_frame_id << ",";
    ss << "\"camera_status\":" << result->camera_status << ",";
    ss << "\"drowsiness_type\":" << result->drowsiness_status << ",";
    ss << "\"distraction_type\":" << result->distraction_status << ",";
    ss << "\"calibrate_status\":" << result->calibrate_status << ",";
    ss << "\"system_status\":" << result->system_status << ",";
    ss << "\"car_speed\":" << car_info_.speed << ",";
    ss << "\"car_gear\":" << car_info_.gear << ",";
    ss << "\"car_steer_whl_snsr_rad\":" << car_info_.steer_whl_snsr_rad << ",";
    ss << "\"turn_light\":" << car_info_.turn_light << ",";
    ss << "\"distraction_params\":" << dms_process_.GetDistractParamers() << ",";
    ss << "\"distraction_reason\": \"" << dms_process_.GetDistractReason(result->camera_status)
       << "\",";
    ss << "\"sdk_version\":\"" << TXDmsGetVersion() << "\",";
    ss << "\"face_info\":{"
       << "\"score\":" << result->face_info.score << ",";
    ss << "\"yaw\":" << result->face_info.head_yaw << ",";
    ss << "\"pitch\":" << result->face_info.head_pitch << ",";
    ss << "\"roll\":" << result->face_info.head_roll << ",";
    ss << "\"isMask\":" << result->face_info.isMask << ",";
    ss << "\"isIRBlock\":" << result->face_info.isIRBlock << ",";
    ss << "\"isGlass\":" << result->face_info.isGlass << ",";
    ss << "\"right_close_eye_score\":" << result->face_info.right_close_eye_score << ",";
    ss << "\"left_close_eye_score\":" << result->face_info.left_close_eye_score << ",";
    ss << "\"mouth_opening\":" << result->face_info.mouth_opening << ",";
    ss << "\"right_eye_score\":" << result->face_info.right_eye_landmark.eye_score << ",";
    ss << "\"right_eye_iris_score\":" << result->face_info.right_eye_landmark.iris_score << ",";
    ss << "\"right_eye_pupil_score\":" << result->face_info.right_eye_landmark.pupil_score << ",";
    ss << "\"right_eye_yaw\":" << result->face_info.right_eye_landmark.yaw << ",";
    ss << "\"right_eye_pitch\":" << result->face_info.right_eye_landmark.pitch << ",";
    ss << "\"left_eye_score\":" << result->face_info.left_eye_landmark.eye_score << ",";
    ss << "\"left_eye_iris_score\":" << result->face_info.left_eye_landmark.iris_score << ",";
    ss << "\"left_eye_pupil_score\":" << result->face_info.left_eye_landmark.pupil_score << ",";
    ss << "\"left_eye_yaw\":" << result->face_info.left_eye_landmark.yaw << ",";
    ss << "\"left_eye_pitch\":" << result->face_info.left_eye_landmark.pitch << "}";
    ss << "}";
    // printf("[%s][SDK-LOG][%s] \n", getCurrentTime().c_str(), ss.str().c_str());
    // auto endtime = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> diff = endtime - starttime;
    // std::cout << "日志所耗时间为：" << diff.count() << "ms" << std::endl;
    
    std::string filename = "/tmp/sdk-log.txt";
    std::ofstream outfile;
    outfile.open(filename, std::ios::out | std::ios::app);
    // 检查文件是否成功打开
    if (outfile.is_open()) {
        outfile << ss.str() << std::endl;
        outfile << cost_time.count() << " ms" << std::endl;
    }
    // 关闭文件
    outfile.close();

    //save image
    std::string picture_path = "/fatfs/app/";
    std::string pic_name;
    if (result->camera_status == Camera_Occlusion) {
        cv::Mat img = cv::Mat(image->height, image->width, CV_8UC1, image->data);
        pic_name = "camera_occ.png";
        if (id % 100 == 0)
            cv::imwrite(picture_path + pic_name, img);
    } else if (result->camera_status == Face_Occlusion) {
        cv::Mat img = cv::Mat(image->height, image->width, CV_8UC1, image->data);
        pic_name = "face_occ.png";
        if (id % 100 == 0)
            cv::imwrite(picture_path + pic_name, img);
    }
    id++;
#endif

    // SaveResultToJson(*result, image);  //保存结果输出
    SaveResultToWeb(*result, image);
    
    //帧率控制
    gettimeofday(&tv, NULL);
    long end_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    long time_gap = end_ts - start_ts;
    if (time_gap < KExpectExcuteTimeMs) {
        long sleep_time_us = (KExpectExcuteTimeMs - time_gap) * 1000 - 200;
        if (sleep_time_us > 0) {
        usleep(sleep_time_us);
        // printf("frame %lld control wait time:%lds \n", result->result_frame_id, sleep_time_us/1000);
        }
    }

    return iRet;
}

int CcDmsProcess::SetInput(const TXImageInfo* image, TXDmsResult* result, long ts) {
    std::shared_ptr<NumArray> image_numArray(new NumArray());
    image_numArray->type = tongxing::NumArray::UINT8;
    image_numArray->word_size = 1;
    image_numArray->data = (unsigned char*)image->data;
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(1);
    image_numArray->shape.push_back(image->height);
    image_numArray->shape.push_back(image->width);
    return once_process(image_numArray, result, ts);
}

int CcDmsProcess::SetInputByEaTensor(void* image, int device, long long frame_id, int speed) {
#ifdef AMBA_SDK
    std::shared_ptr<NumArray> image_numArray(new NumArray());
    image_numArray->type = tongxing::NumArray::UINT8;
    image_numArray->word_size = 1;
    if (device != EA_CPU) {
        ea_tensor_sync_cache((ea_tensor_t*)image, device, EA_CPU);
    }
    image_numArray->data = (unsigned char*)ea_tensor_data((ea_tensor_t*)image);
    const size_t* shape = ea_tensor_shape((ea_tensor_t*)image);
    image_numArray->data_handle = image;
    image_numArray->shape.push_back(shape[0]);
    image_numArray->shape.push_back(shape[1]);
    image_numArray->shape.push_back(shape[2]);
    image_numArray->shape.push_back(shape[3]);
    return once_process(image_numArray, frame_id, speed);
#else
    TX_LOG_ERROR("TX DMS", "SetInputByEaTensor not support");
    return -1;
#endif
}

int CcDmsProcess::SetDriverRoi(TXPoint2i* left_top_point, TXPoint2i* right_bottom_point) {
    /*
    driver_roi_left_top_point = *left_top_point;
    driver_roi_right_bottom_point = *right_bottom_point;
    */

    return 0;
}

int CcDmsProcess::SetPhoneAndSmokingDetectEnableStatus(unsigned char flag) {
    return dms_process_.SetPhoneAndSmokingDetectEnableStatus(flag);
}

int CcDmsProcess::SetDmsState(bool state) {
    return dms_process_.SetFatigue(state);
}

bool CcDmsProcess::GetDmsState() {
    return dms_process_.GetFatigue();
}

const std::string& CcDmsProcess::GetBuildType() const{
    return build_type_;
}

int CcDmsProcess::RestAlarm() {
    return dms_process_.DrowsiRestAlarm();
}

int CcDmsProcess::RestDistractAlarm() {
    return dms_process_.DmsDistractRestAlarm();
}

int CcDmsProcess::AlarmSetOk() {
    return dms_process_.DmsAlarmSetOk();
}

int CcDmsProcess::once_process(std::shared_ptr<NumArray> image, TXDmsResult* result) {
    std::vector<std::shared_ptr<tongxing::NumArray>> input;
    std::shared_ptr<tongxing::NumArray> face_roi =
        creat_numarray({1, 4}, NumArray::DataType::FLOAT32);
    float* face_roi_data_ptr = (float*)face_roi->data;
    face_roi_data_ptr[0] = driver_roi_left_top_point.x;
    face_roi_data_ptr[1] = driver_roi_left_top_point.y;
    face_roi_data_ptr[2] = driver_roi_right_bottom_point.x - driver_roi_left_top_point.x;
    face_roi_data_ptr[3] = driver_roi_right_bottom_point.y - driver_roi_left_top_point.y;
    // TX_LOG_FATAL("TX DMS", "%f %f %f %f",face_roi_data_ptr[0],face_roi_data_ptr[1],face_roi_data_ptr[2],face_roi_data_ptr[3]);
    std::shared_ptr<tongxing::NumArray> exact_face_roi =
    creat_numarray({1, 5}, NumArray::DataType::FLOAT32);
    float* exact_face_roi_data_ptr = (float*)exact_face_roi->data; 
    //index0 ==0表示图像坐标原点为远摄像头点，==1表示图像坐标原点为近摄像头点
    #if defined(BYD_EQ) || defined(BYD_HA6) || defined(BYD_SC3E) || defined(BYD_SC3E_R)
    exact_face_roi_data_ptr[0] = 0;
    exact_face_roi_data_ptr[1] = 380; 
    exact_face_roi_data_ptr[2] = 180;
    exact_face_roi_data_ptr[3] = 500;
    exact_face_roi_data_ptr[4] = 300;
    #elif defined(BYD_EQ_R)
    exact_face_roi_data_ptr[0] = 1;
    exact_face_roi_data_ptr[1] = 380; 
    exact_face_roi_data_ptr[2] = 210;
    exact_face_roi_data_ptr[3] = 500;
    exact_face_roi_data_ptr[4] = 300;
    #else
    exact_face_roi_data_ptr[0] = 0;
    exact_face_roi_data_ptr[1] = 380; 
    exact_face_roi_data_ptr[2] = 210;
    exact_face_roi_data_ptr[3] = 500;
    exact_face_roi_data_ptr[4] = 300;
    #endif
    input.push_back(image);
    input.push_back(face_roi);
    input.push_back(exact_face_roi);

    grop->setInput(input);
    // 处理新帧时更新缓存
    preloadCache();
    // 获取司机人脸bbox
    if (status_process_ == 0)  // DMS_PROCESS
    {
        dms_process_.execute(&car_info_, frame_id++, *result);
    }

    return 0;
}

int CcDmsProcess::once_process(std::shared_ptr<NumArray> image, TXDmsResult* result, long ts) {
    std::vector<std::shared_ptr<tongxing::NumArray>> input;
    std::shared_ptr<tongxing::NumArray> face_roi =
        creat_numarray({1, 4}, NumArray::DataType::FLOAT32);
    float* face_roi_data_ptr = (float*)face_roi->data;
    face_roi_data_ptr[0] = driver_roi_left_top_point.x;
    face_roi_data_ptr[1] = driver_roi_left_top_point.y;
    face_roi_data_ptr[2] = driver_roi_right_bottom_point.x - driver_roi_left_top_point.x;
    face_roi_data_ptr[3] = driver_roi_right_bottom_point.y - driver_roi_left_top_point.y;
    // TX_LOG_FATAL("TX DMS", "%f %f %f %f",face_roi_data_ptr[0],face_roi_data_ptr[1],face_roi_data_ptr[2],face_roi_data_ptr[3]);
    std::shared_ptr<tongxing::NumArray> exact_face_roi =
    creat_numarray({1, 5}, NumArray::DataType::FLOAT32);
    float* exact_face_roi_data_ptr = (float*)exact_face_roi->data;
    //index0 ==0表示图像坐标原点为远摄像头点，==1表示图像坐标原点为近摄像头点
    #if defined(BYD_EQ) || defined(BYD_HA6) || defined(BYD_SC3E) || defined(BYD_SC3E_R)
    exact_face_roi_data_ptr[0] = 0;
    exact_face_roi_data_ptr[1] = 380; 
    exact_face_roi_data_ptr[2] = 180;
    exact_face_roi_data_ptr[3] = 500;
    exact_face_roi_data_ptr[4] = 300;
    #elif defined(BYD_EQ_R)
    exact_face_roi_data_ptr[0] = 1;
    exact_face_roi_data_ptr[1] = 380; 
    exact_face_roi_data_ptr[2] = 210;
    exact_face_roi_data_ptr[3] = 500;
    exact_face_roi_data_ptr[4] = 300;
    #else
    exact_face_roi_data_ptr[0] = 0;
    exact_face_roi_data_ptr[1] = 380; 
    exact_face_roi_data_ptr[2] = 210;
    exact_face_roi_data_ptr[3] = 500;
    exact_face_roi_data_ptr[4] = 300;
    #endif
    input.push_back(image);
    input.push_back(face_roi);
    input.push_back(exact_face_roi);

    grop->setInput(input);
    // 处理新帧时更新缓存
    preloadCache();
    // 获取司机人脸bbox
    if (status_process_ == 0)  // DMS_PROCESS
    {
        dms_process_.execute(&car_info_, frame_id++, *result, ts);
    }

    return 0;
}

std::shared_ptr<CcObjBBox> CcDmsProcess::get_driving_face_bbox() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_bbox start");
    // auto start_time = std::chrono::system_clock::now();
    std::shared_ptr<CcObjBBox> ret(new CcObjBBox);
    auto driving_face_bbox_na = getCachedOutput(0);
    if (driving_face_bbox_na->shape[1] > 0) {
        float* driving_face_bbox = (float*)driving_face_bbox_na->data;
        ret->score = driving_face_bbox[0];
        ret->label = driving_face_bbox[1];
        ret->bbox.x = driving_face_bbox[2];
        ret->bbox.y = driving_face_bbox[3];
        ret->bbox.width = driving_face_bbox[4];
        ret->bbox.height = driving_face_bbox[5];
    } else {
        ret->score = 0;
    }
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "人脸检测模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_bbox end");
    TX_LOG_DEBUG(
        "TX DMS",
        "ret->score=%f ret->bbox.x=%d ret->bbox.y=%d ret->bbox.width=%d ret->bbox.height=%d",
        ret->score, ret->bbox.x, ret->bbox.y, ret->bbox.width, ret->bbox.height);
    return ret;
}

std::shared_ptr<std::vector<cv::Point2f>> CcDmsProcess::get_driving_face_keypoint() {
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_keypoint start");
    // auto start_time = std::chrono::system_clock::now();
    std::shared_ptr<std::vector<cv::Point2f>> ret(new std::vector<cv::Point2f>);
    auto face_keypoint = getCachedOutput(1)->getTensor<float>();
    auto face_keypoint_ptr = face_keypoint->data_;
    for (int i = 0; i < face_keypoint->shape()[1]; i++) {
        cv::Point2f p;
        p.x = face_keypoint_ptr[3 * i + 1];
        p.y = face_keypoint_ptr[3 * i + 2];

        ret->push_back(p);
    }
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "人脸关键点模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_keypoint end");
    return ret;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_face_angle() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_angle start");
    // auto start_time = std::chrono::system_clock::now();
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    auto face_angle = (float*)getCachedOutput(4)->data;
    auto key_points = getCachedOutput(1)->getTensor<float>()->operator[](0);
    int center_x = (key_points[16][1].get() + key_points[18][1].get()) / 2;
    int nose_x = (key_points[4][1].get());
    // std::cout<<center_x <<" "<< nose_x<<" "<<face_angle[1]<<std::endl;
    ret->push_back(face_angle[0]);
    ret->push_back(face_angle[1]);
    ret->push_back(face_angle[2]);
    ret->push_back(face_angle[3]);
    ret->push_back(face_angle[4]);

    if (key_points[16][0].get() >= 0.5 && key_points[18][0].get() >= 0.5) {
        if (abs(ret->operator[](1)) >= 50) {
            if ((center_x - nose_x) > 0) {
                if (ret->operator[](1) < 0) {
                    ret->operator[](1) = -ret->operator[](1);
                }
            } else if ((center_x - nose_x) < 0) {
                if (ret->operator[](1) > 0) {
                    ret->operator[](1) = -ret->operator[](1);
                }
            }
        }
    }

    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "获取人脸角度模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_angle end %f %f %f", face_angle[0],
                 face_angle[1], face_angle[2]);
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_angle end");
    return ret;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_right_eye_close_score() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_right_eye_close_score start");
    // auto start_time = std::chrono::system_clock::now();
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    float* right_eye_close_score = (float*)getCachedOutput(2)->data;
    ret->push_back(right_eye_close_score[0]);
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "获取右眼闭眼分数模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_right_eye_close_score %f %f",
                 right_eye_close_score[0], right_eye_close_score[1]);
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_right_eye_close_score end");
    return ret;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_left_eye_close_score() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_left_eye_close_score start");
    // auto start_time = std::chrono::system_clock::now();
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    float* left_eye_close_score = (float*)getCachedOutput(3)->data;
    ret->push_back(left_eye_close_score[0]);
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "获取左眼闭眼分数模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_left_eye_close_score %f %f",
                 left_eye_close_score[0], left_eye_close_score[1]);
    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_left_eye_close_score end");
    return ret;
}
int CcDmsProcess::get_occlusion_status() {
#ifndef ONLY_LANDMARKS
    // auto start_time = std::chrono::system_clock::now();
    float occlusion_status = ((float*)getCachedOutput(5)->data)[0];
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "摄像头遮挡检测模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    return occlusion_status;
#else
    return false;
#endif
}

void CcDmsProcess::setVerifyPupilPos(std::vector<float>& verify_pupil_pos) {
    l_pupil_pos_x_verify = verify_pupil_pos[0];
    l_pupil_pos_y_verify = verify_pupil_pos[1];
    r_pupil_pos_x_verify = verify_pupil_pos[2];
    r_pupil_pos_y_verify = verify_pupil_pos[3];
    return;
}

// 获取关键点稳定性分析结果
std::shared_ptr<KeypointsStabilityAnalysisResult> CcDmsProcess::get_keypoints_stability_analysis() {
    auto result = std::make_shared<KeypointsStabilityAnalysisResult>();
    result->keypoint_instability = KeypointsStabilityAnalysisResult::KeypointInstabilityStatus();

    // 排除特定关键点：16、17、18以及大于21的点
    std::vector<int> exclude_indices;
    exclude_indices.push_back(16);
    exclude_indices.push_back(17);
    exclude_indices.push_back(18);
    for (int i = 22; i < 38; i++) {
        exclude_indices.push_back(i);
    }

    auto face_kp_instab_scores = get_face_keypoints_instability_scores(true, exclude_indices);  // 排除指定关键点

    // 统计不稳定关键点数量和计算总评分
    float avg_instability_score = -1.0f;
    if (face_kp_instab_scores && !face_kp_instab_scores->empty()) {
        float total_instability_score = 0.0f;

        // 计算总的不稳定性评分并统计不稳定关键点数量
        for (const auto& info : *face_kp_instab_scores) {
            total_instability_score += info.score;
        }
    
        // 计算平均不稳定性评分
        avg_instability_score = total_instability_score / face_kp_instab_scores->size();

        const float face_kp_instab_thr = 0.4f; // 这是在假设v>8,20的base,0.6的thr,s<0.6,0.4的thr上得到的值
        // 与平均值比较并打印比平均值更大的关键点
        if (avg_instability_score > 0.0f) {
            int upper_avg_count = 0;
            for (const auto& info : *face_kp_instab_scores) {
                if (info.score > avg_instability_score) {
                    // printf("  Keypoint %d: score=%.3f (%.3f upper avg)\n",
                    //        info.index, info.score, info.score - avg_instability_score);

                    upper_avg_count++;
                    // if (info.score > face_kp_instab_thr)
                    //     printf("    Keypoint %d is unstable\n", info.index);
                }
            }
            // printf("Total %d keypoints below average instability score (from %d valid keypoints)\n",
            //        upper_avg_count, (int)face_kp_instab_scores->size());
        }

        // 检测面部区域级别的不稳定性
        auto region_instability = get_facial_regions_instability_status(face_kp_instab_scores, face_kp_instab_thr, exclude_indices);

        // 根据区域不稳定性设置关键点不稳定状态
        if (region_instability && !region_instability->empty() && avg_instability_score > 0.0f) {        
            for (const auto& region_info : *region_instability) {
                switch (region_info.region) {
                    // case FacialRegion::LEFT_EYE:
                    //     result->keypoint_instability.left_eye_unstable = region_info.is_unstable;
                    //     break;
                        
                    // case FacialRegion::RIGHT_EYE:
                    //     result->keypoint_instability.right_eye_unstable = region_info.is_unstable;
                    //     break;
                        
                    case FacialRegion::NOSE:
                        result->keypoint_instability.nose_unstable = region_info.is_unstable;
                        break;
                        
                    case FacialRegion::MOUTH:
                        result->keypoint_instability.mouth_unstable = region_info.is_unstable;
                        break;
                        
                    case FacialRegion::NOSE_BRIDGE:
                        result->keypoint_instability.nose_bridge_unstable = region_info.is_unstable;
                        break;
                        
                    default:
                        break;
                }
                // printf("  %s: %d/%d unstable points (%.1f%%) - Region %s\n",
                //     region_info.region_name.c_str(),
                //     region_info.unstable_points,
                //     region_info.total_points,
                //     region_info.unstable_ratio * 100.0f,
                //     region_info.is_unstable ? "UNSTABLE" : "STABLE");
            }
        }
    }

    // 获取排序后的眼部关键点不稳定性评估数据
    auto left_eye_scores = get_left_eye_keypoints_instability_scores(true);   // 按评分排序：高->低
    auto right_eye_scores = get_right_eye_keypoints_instability_scores(true); // 按评分排序：高->低
    
    // 处理左眼关键点质量分析
    result->keypoint_instability.left_eye_keypoints_unstable = false;
    result->keypoint_instability.right_eye_keypoints_unstable = false;

    if (left_eye_scores && !left_eye_scores->empty() && avg_instability_score > 0.0f) {
        // 计算平均不稳定性评分
        float total_score = 0.0f;
        for (const auto& info : *left_eye_scores) {
            total_score += info.score;
        }
        const float leye_kp_instab_thr = 0.4f;
        float instability_score = total_score / left_eye_scores->size();
        result->keypoint_instability.left_eye_keypoints_unstable = instability_score > leye_kp_instab_thr;

        // 眼部区域稳定性分析
        auto left_eye_region_analysis = get_eye_regions_instability_status(left_eye_scores, leye_kp_instab_thr);

        if (left_eye_region_analysis && !left_eye_region_analysis->empty()) {        
            for (const auto& region_info : *left_eye_region_analysis) {
                switch (region_info.region) {
                    case EyeRegion::EYE_CONTOUR:
                        result->keypoint_instability.left_eye_keypoints_unstable = region_info.is_unstable;
                        break;
                        
                    // case EyeRegion::IRIS_CONTOUR:
                    //     result->keypoint_instability.right_eye_unstable = region_info.is_unstable;
                    //     break;
                        
                    // case EyeRegion::PUPIL:
                    //     result->keypoint_instability.nose_unstable = region_info.is_unstable;
                    //     break;

                    default:
                        break;
                }
                // printf("  %s: %d/%d unstable points (%.1f%%) - Region %s\n",
                //     region_info.region_name.c_str(),
                //     region_info.unstable_points,
                //     region_info.total_points,
                //     region_info.unstable_ratio * 100.0f,
                //     region_info.is_unstable ? "UNSTABLE" : "STABLE");
            }
        }       

        // std::cout << "TX DMS Left eye keypoints quality analysis:" << std::endl;
        // std::cout << "  Average instability: " << instability_score
        //           << ", Unstable: " << (result->keypoint_instability.left_eye_keypoints_unstable ? "YES" : "NO")
        //           << ", Total keypoints: " << left_eye_scores->size() << std::endl;

        // std::cout << "  Keypoints ranked by instability score (high to low):" << std::endl;
        // for (const auto& info : *left_eye_scores) {
        //     std::string status = info.score > leye_kp_instab_thr ? "UNSTABLE" : "STABLE";
        //     std::cout << "    Keypoint[" << info.index << "]: score=" << info.score 
        //               << " (" << status << ")" << std::endl;
        // }
    }
    
    // 处理右眼关键点质量分析
    if (right_eye_scores && !right_eye_scores->empty() && avg_instability_score > 0.0f) {
        // 计算平均不稳定性评分
        float total_score = 0.0f;
        for (const auto& info : *right_eye_scores) {
            total_score += info.score;
        }

        const float reye_kp_instab_thr = 0.4f;
        float instability_score = total_score / right_eye_scores->size();
        result->keypoint_instability.right_eye_keypoints_unstable = instability_score > reye_kp_instab_thr;
        
        // 眼部区域稳定性分析
        auto right_eye_region_analysis = get_eye_regions_instability_status(right_eye_scores, reye_kp_instab_thr);
        if (right_eye_region_analysis && !right_eye_region_analysis->empty()) {        
            for (const auto& region_info : *right_eye_region_analysis) {
                switch (region_info.region) {
                    case EyeRegion::EYE_CONTOUR:
                        result->keypoint_instability.right_eye_keypoints_unstable = region_info.is_unstable;
                        break;
                        
                    // case EyeRegion::IRIS_CONTOUR:
                    //     result->keypoint_instability.right_eye_unstable = region_info.is_unstable;
                    //     break;
                        
                    // case EyeRegion::PUPIL:
                    //     result->keypoint_instability.nose_unstable = region_info.is_unstable;
                    //     break;

                    default:
                        break;
                }
                // printf("  %s: %d/%d unstable points (%.1f%%) - Region %s\n",
                //     region_info.region_name.c_str(),
                //     region_info.unstable_points,
                //     region_info.total_points,
                //     region_info.unstable_ratio * 100.0f,
                //     region_info.is_unstable ? "UNSTABLE" : "STABLE");
            }
        }  

        // std::cout << "TX DMS Right eye keypoints quality analysis:" << std::endl;
        // std::cout << "  Average instability: " << instability_score
        //           << ", Unstable: " << (result->keypoint_instability.right_eye_keypoints_unstable ? "YES" : "NO")
        //           << ", Total keypoints: " << right_eye_scores->size() << std::endl;
        // std::cout << "  Keypoints ranked by instability score (high to low):" << std::endl;
        // for (const auto& info : *right_eye_scores) {
        //     std::string status = info.score > reye_kp_instab_thr ? "UNSTABLE" : "STABLE";
        //     std::cout << "    Keypoint[" << info.index << "]: score=" << info.score 
        //               << " (" << status << ")" << std::endl;
        // }
    } 

    return result;
}

// 获取图像某个roi区域的图像亮度信息（带旋转角度）
std::shared_ptr<std::vector<float>> CcDmsProcess::get_exact_lum_info() {
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    std::vector<float> verify_pupil_pos(4, -1.0f);

    float* face_exact_lum_info = (float*)getCachedOutput(11)->data;
    int num = 3, step = 5;
    for (int i = 0; i < num; i++) {
        for (int j = 0; j < step; j++) {
            if (j < 3) {
                ret->push_back(face_exact_lum_info[i * step + j]);
                // std::cout << "i * step + j:" << i * step + j << std::endl;
            } else if (i != 2 && (j == 3 || j == 4)) {
                // std::cout << "j-3:" << j-3 << " i * step + j:" << i * step + j << std::endl;
                verify_pupil_pos[i*2+(j-3)] = face_exact_lum_info[i * step + j];                
            }
        }
    }
    // 如果verify_pupil_pos有小于0的值，则verify_pupil_pos里面全部的内容置为-1，其它元素也要
    {
        bool need_reset = false;
        for (int i = 0; i < 4; i++) {
            if (verify_pupil_pos[i] < 0) {
                need_reset = true;
                break;
            }
        }
        if (need_reset) {
            for (int i = 0; i < 4; i++) {
                verify_pupil_pos[i] = -1.0f;
            }
        }
    }
    
    setVerifyPupilPos(verify_pupil_pos);

    return ret;
}
inline bool cmp_cv_point_x_max(cv::Point& p1, cv::Point& p2) {
    return p1.x > p2.x;
}
inline bool cmp_cv_point_x_min(cv::Point& p1, cv::Point& p2) {
    return p1.x < p2.x;
}
inline bool cmp_cv_point_y_max(cv::Point& p1, cv::Point& p2) {
    return p1.y > p2.y;
}
inline double findFarthestDistance(const std::vector<cv::Point>& points,
                                   cv::Point& p1,
                                   cv::Point& p2) {
    double maxDistance = 0;
    for (size_t i = 0; i < points.size(); i++) {
        for (size_t j = i + 1; j < points.size(); j++) {
            double distance = cv::norm(points[i] - points[j]);
            if (distance > maxDistance) {
                maxDistance = distance;
                p1 = points[j];
                p2 = points[i];
            }
        }
    }
    return maxDistance;
}

inline float pointToLineDistance(const cv::Point2f& pt,
                               const cv::Point2f& linePt1,
                               const cv::Point2f& linePt2) {
    // 计算直线向量
    float dx = linePt2.x - linePt1.x;
    float dy = linePt2.y - linePt1.y;
    // 计算点到直线起点向量的叉积
    float crossProduct = (pt.x - linePt1.x) * dy - (pt.y - linePt1.y) * dx;
    // 计算直线向量的长度
    float length = std::sqrt(dx * dx + dy * dy);
    // 计算点到直线的距离
    return std::abs(crossProduct) / length;
}
inline int findMaxValue(const std::vector<int>& vec) {
    // 初始化最大值为int能表示的最小值
    int maxValue = std::numeric_limits<int>::min();
    // 遍历vector，更新最大值
    for (int value : vec) {
        if (value > maxValue) {
            maxValue = value;
        }
    }
    return maxValue;
}
inline bool get_eye_info(const std::vector<cv::Point2f>& input,
                         const std::shared_ptr<std::vector<float>>& angle,
                         cv::RotatedRect& output_rect,
                         std::vector<cv::Point2f>& output_point,
                         float& opening,
                         float& up_down_proportion) {
    cv::Point2f p1;
    cv::Point2f p2;
    std::vector<cv::Point2f> sim_point;

    if (input[4].x > input[0].x) {
        sim_point = input;
    } else {
        sim_point.push_back(input[4]);
        sim_point.push_back(input[5]);
        sim_point.push_back(input[6]);
        sim_point.push_back(input[7]);
        sim_point.push_back(input[0]);
        sim_point.push_back(input[1]);
        sim_point.push_back(input[2]);
        sim_point.push_back(input[3]);
    }

    p1 = sim_point[0];
    p2 = sim_point[4];

    float roll = angle->operator[](2);
    output_rect.center = (p1 + p2) / 2;
    output_rect.angle = roll;

    output_rect.size.width = cv::norm(p2 - p1);

    cv::Point center_point = (p1 + p2) / 2;
    float up_value = pointToLineDistance(sim_point[2], p1, p2);
    float down_value = pointToLineDistance(sim_point[6], p1, p2);

    output_rect.size.height = up_value + down_value;
    if (cv::norm(p2 - p1) < 0) {
        opening = 0;
    } else {
        opening = cv::norm(sim_point[2] - sim_point[6]) / (cv::norm(p2 - p1) / 2);
    }
    if (up_value == 0 && down_value == 0) {
        up_down_proportion = 1;
    } else {
        up_down_proportion = up_value / (up_value + down_value);
    }
    output_point.clear();
    output_point.push_back(p1);
    output_point.push_back(p2);
    return true;
}

inline bool get_pupilpos_in_eye(const std::vector<cv::Point2f>& key_points,
                                float& pupil_to_up_down_dis,
                                float& pupil_to_left_right_dis) {
    pupil_to_up_down_dis = 0.0f;
    pupil_to_left_right_dis = 0.0f;

    if (key_points.size() != 5)
        return false;

    // 传入的点集需要是已经经过仿射变换的
    cv::Point2f pupil_point = key_points[2];
    cv::Point2f up_point = key_points[3];
    cv::Point2f down_point = key_points[4];
    cv::Point2f left_point = key_points[0];
    cv::Point2f right_point = key_points[1];

    // 校正up/down/left/right点
    if (key_points[3].y > key_points[4].y) {
        up_point = key_points[4];
        down_point = key_points[3];
    }
    if (key_points[0].x > key_points[1].x) {
        left_point = key_points[1];
        right_point = key_points[0];
    }
    // std::cout << "pupil_point:" << pupil_point << " up_point:" << up_point << " down_point:" << down_point << std::endl;
    if (pupil_point.y >= up_point.y && pupil_point.y <= down_point.y) {
        float up_value = std::abs(pupil_point.y - up_point.y);
        float down_value = std::abs(pupil_point.y - down_point.y);
        pupil_to_up_down_dis = up_value / (up_value + down_value);
    }
    if (pupil_point.x >= left_point.x && pupil_point.x <= right_point.x) {
        float left_value = std::abs(pupil_point.x - left_point.x);
        float right_value = std::abs(pupil_point.x - right_point.x);
        pupil_to_left_right_dis = left_value / (left_value + right_value);
    }

    return true;
}
bool CcDmsProcess::checkKeypointInValid(const std::vector<cv::Point2f>& point_set,
                                        const std::shared_ptr<std::vector<float>>& angle,
                                        const std::shared_ptr<std::vector<cv::Point2f>>& keypoints,
                                        std::deque<float>& eye_lcorner_pos_queue,
                                        std::deque<float>& eye_rcorner_pos_queue,
                                        std::deque<float>& headangle_queue) {
    bool is_eyekeypoints_invalid = false;

    // static int test_count = 0;
    // std::cout << "point_set[0]:[" << point_set[0].x << ", " << point_set[0].y << "]"
    //           << "  point_set[1]:[" << point_set[1].x << ", " << point_set[1].y << "]"
    //           << " current headyaw:" << angle->operator[](1)
    //           << " current headpitch:" << angle->operator[](0) << std::endl;
    constexpr int MaxSize = 5;
// #if defined(BYD_HKH_R) || defined(BYD_SA2)
//     constexpr float HeadUndulation = 1.5f;
//     constexpr float EyeUndulation = 10.0f;
// #else
    constexpr float HeadUndulation = 2.0f;
    constexpr float EyeUndulation = 4.5f;

    auto nose_tip = keypoints->operator[](4);
    auto philtrum_1 = keypoints->operator[](9);
    auto philtrum_2 = keypoints->operator[](10);
    auto philtrum_3 = keypoints->operator[](11);
    auto mouth_left_coner = keypoints->operator[](5);
    auto mouth_right_coner = keypoints->operator[](7);

    auto headyaw = angle->operator[](1);
    auto headpitch = angle->operator[](0);
    auto headangle = std::sqrt(headyaw * headyaw + headpitch * headpitch);
    // 选取相对于不受光照环境影响，且相对于人脸生理特征比较稳定的关键点
    cv::Point2f face_average_point =
        (nose_tip + philtrum_1 + philtrum_2 + philtrum_3 + mouth_left_coner + mouth_right_coner) /
        6;
    auto eye_l_corner_dis = cv::norm(face_average_point - point_set[0]);
    auto eye_r_corner_dis = cv::norm(face_average_point - point_set[1]);

    // 计算与历史均值的差异
    float dis1_diff = 0, dis2_diff = 0, headangle_diff = 0;
    if (eye_lcorner_pos_queue.size() >= MaxSize) {
        float dis1_mean =
            std::accumulate(eye_lcorner_pos_queue.begin(), eye_lcorner_pos_queue.end(), 0.0f) /
            eye_lcorner_pos_queue.size();
        dis1_diff = eye_l_corner_dis - dis1_mean;
    }
    if (eye_rcorner_pos_queue.size() >= MaxSize) {
        float dis2_mean =
            std::accumulate(eye_rcorner_pos_queue.begin(), eye_rcorner_pos_queue.end(), 0.0f) /
            eye_rcorner_pos_queue.size();
        dis2_diff = eye_r_corner_dis - dis2_mean;
    }
    if (headangle_queue.size() >= MaxSize) {
        float headangle_mean =
            std::accumulate(headangle_queue.begin(), headangle_queue.end(), 0.0f) /
            headangle_queue.size();
        headangle_diff = headangle - headangle_mean;
    }
    // 更新历史数据队列
    eye_lcorner_pos_queue.push_back(eye_l_corner_dis);
    eye_rcorner_pos_queue.push_back(eye_r_corner_dis);
    headangle_queue.push_back(headangle);

    if (eye_lcorner_pos_queue.size() > MaxSize && eye_rcorner_pos_queue.size() > MaxSize &&
        headangle_queue.size() > MaxSize) {
        eye_lcorner_pos_queue.pop_front();
        eye_rcorner_pos_queue.pop_front();
        headangle_queue.pop_front();
    }
    if (abs(dis1_diff) + abs(dis2_diff) >= EyeUndulation && abs(headangle_diff) <= HeadUndulation) {
        is_eyekeypoints_invalid = true;
        // test_count++;
        // printf("\033[33m --hjh-- file:calmcar_dms_process.cpp line:%d info:eyes keypoint "
        //        "maybe invalid... %d \033[0m \n ",
        //        __LINE__, test_count);
    }
    // std::cout << "face_average_point:" << face_average_point
    //           << " eye_l_corner_dis:" << eye_l_corner_dis
    //           << " eye_r_corner_dis:" << eye_r_corner_dis << " dis1_diff:" << dis1_diff
    //           << " dis2_diff:" << dis2_diff << " headangle_diff:" << headangle_diff << std::endl;
    // std::cout << "differ:[" << dis1_diff << " , " << dis2_diff << " , " << headangle_diff
    //           << " ]" << std::endl;

    return is_eyekeypoints_invalid;
}

// 计算三点曲率
float calcCurvature(const cv::Point& p1, const cv::Point& p2, const cv::Point& p3) {
    const float a = cv::norm(p1 - p2);
    const float b = cv::norm(p2 - p3);
    const float c = cv::norm(p3 - p1);
    const float area = ((p2.x - p1.x)*(p3.y - p1.y) - (p3.x - p1.x)*(p2.y - p1.y)) / 2.0f; // 保留叉积的正负
    float curve = 0.0f;
    if (a*b*c > 1e-6) {
        curve = 4.0f * area / (a*b*c);
    }
    // std::cout << "[a,b,c]:[" << a << ", " << b << ", " << c << "]; area:" << area << "; curve:" << curve << std::endl;

    return curve;
}

bool CcDmsProcess::eyeCurveProcess(const std::vector<cv::Point2f>& eye_contour, float &curve_score) {

    bool is_curved = true;
    // 更新历史队列
    if(m_eye_contour_history.size() >= HISTORY_SIZE) {
        m_eye_contour_history.pop_front();
    }
    m_eye_contour_history.push_back(eye_contour);

    // 计算时域加权平均（指数衰减）
    std::vector<cv::Point2f> smoothed_contour = eye_contour;
    const float alpha = 0.6f; // 当前帧权重
    for(int i=0; i<smoothed_contour.size(); ++i) {
        float sum_x = 0.0f, sum_y = 0.0f;
        float weight_sum = 0.0f;
        float current_weight = alpha;
        
        // 逆向遍历历史帧（从最新到最旧）
        for(auto it = m_eye_contour_history.rbegin(); it != m_eye_contour_history.rend(); ++it) {
            if(i >= it->size()) continue;
            
            sum_x += current_weight * (*it)[i].x;
            sum_y += current_weight * (*it)[i].y;
            weight_sum += current_weight;
            
            current_weight *= (1 - alpha); // 权重衰减
            if(current_weight < 0.1f) break;
        }
        
        smoothed_contour[i].x = cvRound(sum_x / weight_sum);
        smoothed_contour[i].y = cvRound(sum_y / weight_sum);
    }
    
    float upper_curvature = 0.0f;
    const auto& prev = smoothed_contour[0];
    const auto& curr = smoothed_contour[2];
    const auto& next = smoothed_contour[4]; 
    upper_curvature = calcCurvature(prev, curr, next);
    std::vector<float> upper_curvatures;
    std::vector<float> upper_distances;
    // 上眼皮曲度（点1-3）
    for(int i = 1; i <= 3; ++i) {
        const auto& prev = smoothed_contour[i-1];
        const auto& curr = smoothed_contour[i];
        const auto& next = smoothed_contour[i+1]; 
        upper_curvatures.push_back(calcCurvature(prev, curr, next));
        upper_distances.push_back(cv::norm(prev - curr));
    }

    // // 下眼皮曲度（点5-7）
    // float lower_curvature = 0.0f;
    // const auto& prev_1 = smoothed_contour[0];
    // const auto& curr_1 = smoothed_contour[6];
    // const auto& next_1 = smoothed_contour[4]; 
    // lower_curvature = calcCurvature(prev_1, curr_1, next_1);
    // std::vector<float> lower_curvatures;
    // std::vector<float> lower_distances;
    // for(int i = 5; i <= 7; ++i) {
    //     const auto& prev = smoothed_contour[i-1];
    //     const auto& curr = smoothed_contour[i];
    //     const auto& next = smoothed_contour[i+1]; 
    //     printf("\033[33m --hjh-- file:calmcar_dms_process.cpp line:%d info:smoothed_contour.size():%d \033[0m \n ",__LINE__, smoothed_contour.size());
    //     float tmp_curve = calcCurvature(prev, curr, next);
    //     printf("\033[33m --hjh-- file:calmcar_dms_process.cpp line:%d info:tmp_curve:%f \033[0m \n ",__LINE__, tmp_curve);
    //     lower_curvatures.push_back(tmp_curve);
    //     lower_distances.push_back(cv::norm(prev - curr));
    // }

    // 标准化处理
    // const float inter_eye_dist = cv::norm(smoothed_contour[0] - smoothed_contour[4]);
    // curve_score = upper_curvature /= ( inter_eye_dist);
    // lower_curvature /= ( inter_eye_dist);

    // if (upper_curvature < KMinCurveScore)
    //     is_curved = false;

    float up_curve_0 = upper_curvatures[0] / upper_distances[0];
    float up_curve_1 = upper_curvatures[1] / upper_distances[1];
    float up_curve_2 = upper_curvatures[2] / upper_distances[2];
    float up_mean_curve = (up_curve_0 + up_curve_1) / 2.0f;//(up_curve_0 + up_curve_1 + up_curve_2) / 3.0f;
    curve_score = up_mean_curve;
    // std::cout << "upper curve score list:[" << upper_curvature << ", " << up_mean_curve 
    // << ", " << up_curve_0 << ", " << up_curve_1 << ", " << up_curve_2 << "]."<< std::endl;

    // float lower_curve_0 = lower_curvatures[0] / lower_distances[0];
    // float lower_curve_1 = lower_curvatures[1] / lower_distances[1];
    // float lower_curve_2 = lower_curvatures[2] / lower_distances[2];
    // float lower_mean_curve = (lower_curve_0 + lower_curve_1) / 2.0f;//(lower_curve_0 + lower_curve_1 + lower_curve_2) / 3.0f;
    // std::cout << "lower curve score list:[" << lower_curvature << ", " << lower_mean_curve 
    // << ", " << lower_curve_0 << ", " << lower_curve_1 << ", " << lower_curve_2 << "]."<< std::endl;

    // std::string curve_status = "";
    // is_curved ? curve_status = "curved" : curve_status ="not curved";
    // std::cout << curve_status << ",inter_eye_dist:" << inter_eye_dist << 
    //     " upper_curvature:" << upper_curvature << std::endl;
    //     " lower_curvature:" << lower_curvature << std::endl;
    return is_curved;
}

std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_eye_landmarks(
    const std::shared_ptr<NumArray>& feat,
    const std::shared_ptr<NumArray>& pixels_histogram,
    const std::shared_ptr<std::vector<float>>& angle,
    const std::shared_ptr<std::vector<cv::Point2f>> keypoints,
    const bool is_glasses,
    const bool is_righteye,
    EyeInfo& eye_info) {
    cv::RotatedRect eye_rect;
    float eye_flag = 0;

    cv::RotatedRect iris_rect;
    float iris_flag = 0;

    cv::RotatedRect pupil_rect;
    float pupil_flag = 0;

    float eye_opening = 0;
    float up_down_proportion = 1;
    std::vector<cv::Point2f> eye_max_points;

    float* eye_keypoint_ptr = (float*)feat->data;
    //0-7:eye countour,8-15:iris contour:16 pupil point
    const int EYE_CONTOUR_START = 0;
    const int EYE_CONTOUR_END = 7;
    const int IRIS_CONTOUR_START = 8;
    const int IRIS_CONTOUR_END = 15;
    const int PUPIL_POINT_INDEX = 16;
    
    std::vector<cv::Point2f> eye_contour;
    float eye_score = 1;

    for (int i = EYE_CONTOUR_START; i <= EYE_CONTOUR_END; i++) {
        cv::Point2f p;
        p.x = eye_keypoint_ptr[3 * i + 1];
        p.y = eye_keypoint_ptr[3 * i + 2];
        eye_contour.push_back(p);
        if (eye_score > eye_keypoint_ptr[3 * i]) {
            eye_score = eye_keypoint_ptr[3 * i];
        }
    }

    bool is_curved = true;
    float curve_score = 1.0f;
    if (eye_score > kMinEyeScore) {
        if (get_eye_info(eye_contour, angle, eye_rect, eye_max_points, eye_opening,
                         up_down_proportion)) {
            eye_flag = 1;
            // std::cout<<"eye_opening:"<<eye_opening<<std::endl;
        }
        is_curved = eyeCurveProcess(eye_contour, curve_score);
    }

    std::vector<cv::Point2f> iris_contour;

    float iris_score = 1;
    for (int i = IRIS_CONTOUR_START; i <= IRIS_CONTOUR_END; i++) {
        cv::Point2f p;
        p.x = eye_keypoint_ptr[3 * i + 1];
        p.y = eye_keypoint_ptr[3 * i + 2];
        iris_contour.push_back(p);
        if (iris_score > eye_keypoint_ptr[3 * i]) {
            iris_score = eye_keypoint_ptr[3 * i];
        }
    }

    if (iris_score > kMinIrisScore && eye_flag) {
        iris_rect = cv::fitEllipse(iris_contour);
        iris_flag = 1;
    }

    cv::Point2f pupil;
    
    float pupil_score = eye_keypoint_ptr[3 * PUPIL_POINT_INDEX];
    pupil.x = eye_keypoint_ptr[3 * PUPIL_POINT_INDEX + 1];
    pupil.y = eye_keypoint_ptr[3 * PUPIL_POINT_INDEX + 2];
    // std::cout << "pupil:" << pupil << std::endl;

    // 验证并矫正瞳孔位置
    if (pupil_score > kMinPupilScore && eye_flag) {
        pupil_flag = 1;
        pupil_rect.center = pupil;
        pupil_rect.size.width = 1;
        pupil_rect.size.height = 1;

        // 增加在亮瞳场景下对瞳孔检测不准的矫正
        if (eye_opening > 0.3) {
            if (!is_righteye) {
                validateAndCorrectPupil(l_pupil_pos_x_verify, l_pupil_pos_y_verify,
                                      iris_contour, pupil, pupil_flag);
            } else {
                validateAndCorrectPupil(r_pupil_pos_x_verify, r_pupil_pos_y_verify,
                                      iris_contour, pupil, pupil_flag);
            }
        }
    }    
    // std::cout << "eye_score:" << eye_score << " iris_score:" << iris_score << " pupil_score:" << pupil_score << std::endl;

    if (eye_flag > 0 && pupil_flag > 0) {
        double d = pointPolygonTest(eye_contour, pupil, true);

        if (d < -3) {
            // std::cout<<"D+++++++++++++++++++++++===="<<d<<std::endl;
            pupil_flag = 0;
            iris_flag = 0;
        }
    }
    cv::Point2f target_point;
    float pupil_to_up_down_dis = 0.0;
    float pupil_to_left_right_dis = 0.0;
    if (eye_flag > 0) {
        std::vector<cv::Point2f> point_set;
        point_set.push_back(eye_max_points[0]);
        point_set.push_back(eye_max_points[1]);
        point_set.push_back(pupil);
        point_set.push_back(eye_contour[2]);  // 增加眼睛轮廓的上下关键点
        point_set.push_back(eye_contour[6]);

        cv::Point2f roll_vec = eye_max_points[1] - eye_max_points[0];
        float roll = atan2(roll_vec.y, roll_vec.x) / CV_PI * 180.0f;

        // 根据左右眼角确定中心点，然后计算旋转矩阵进行坐标转换
        cv::Point c_point = (eye_max_points[0] + eye_max_points[1]) / 2;
        cv::Mat rot_mat = cv::getRotationMatrix2D(c_point, roll, 1.0);
        cv::transform(point_set, point_set, rot_mat);

        if (pupil_flag > 0)
            get_pupilpos_in_eye(point_set, pupil_to_up_down_dis, pupil_to_left_right_dis);

        bool is_eyekeypoints_invalid = false;
        // if (is_glasses) {
        //     float headyaw_min = 0.0, headyaw_max = 0.0, headpitch_min = 0.0, headpitch_max = 0.0;
        //     //TODO:这么传有点耦合了。。。，后面看怎样使结构清晰。
        //     dms_process_.getNonDistractHeadRotation(headyaw_min, headyaw_max, headpitch_min,
        //                                             headpitch_max);
        //     auto headyaw = angle->operator[](1);
        //     auto headpitch = angle->operator[](0);
        //     if (headyaw >= headyaw_min && headyaw <= headyaw_max && headpitch >= headpitch_min &&
        //         headpitch <= headpitch_max) {
        //         if (is_righteye) {
        //             is_eyekeypoints_invalid =
        //                 checkKeypointInValid(point_set, angle, keypoints, reye_lcorner_pos_queue,
        //                                      reye_rcorner_pos_queue, rheadangle_queue);
        //         } else {
        //             is_eyekeypoints_invalid =
        //                 checkKeypointInValid(point_set, angle, keypoints, leye_lcorner_pos_queue,
        //                                      leye_rcorner_pos_queue, lheadangle_queue);
        //         }
        //     }

        // }

        if (eye_flag > 0 && pupil_flag > 0) {
            // 坐标系转换，将眼角和瞳孔的坐标转换到以(min_x,min_y)为原点的坐标系下
            float min_x = std::min(point_set[0].x, point_set[1].x);
            float min_y = std::min(point_set[0].y, point_set[1].y);
            float max_x = std::max(point_set[0].x, point_set[1].x);
            float max_y = std::max(point_set[0].y, point_set[1].y);
            for (auto& p : point_set) {
                p.x -= min_x;
                p.y -= (min_y + max_y) / 2;
            }

            float max_value = max_x - min_x;
            target_point = (point_set[2]);

            target_point.x = target_point.x - (max_value / 2);
            target_point.x = -(target_point.x / (max_value / 2) * 85);
            target_point.y = -target_point.y / (max_value / 2) * 30;

            if (is_glasses) {
                // // 对眼睛亮度直方图进行分析
                // int peak_value_index = 0;
                // parsePixelValue(pixels_histogram, peak_value_index);
                // if (peak_value_index > 230) {
                //     std::cout << is_righteye << " eye info is abnormal -1... " << peak_value_index << std::endl;
                //     eye_flag = -1;
                //     pupil_flag = -1;
                //     iris_flag = -1; // 不对yaw,pitch篡改，因为外层还会再判断眼睛的数据是否有效
                //     // targe_point.x = 0;
                //     // targe_point.y = 0;
                // }

                // 对眼睛关键点波动率进行分析
                if (is_eyekeypoints_invalid) {
                    std::cout << is_righteye << " eye info is abnormal -2... " << std::endl;
                    eye_flag = -2;
                    pupil_flag = -2;
                    iris_flag = -2;
                    // targe_point.x = 0;
                    // targe_point.y = 0;
                }
                // if (peak_value_index > 230 && is_eyekeypoints_invalid) {
                //     eye_flag = -3;
                //     pupil_flag = -3;
                //     iris_flag = -3;
                // }
            }
        }
    }

// debug draweye
#if 0
    {
        static int count = 0;
        cv::Point min_point(2000, 2000);
        for (auto p : eye_contour) {
            min_point.x = std::min(p.x, min_point.x);
            min_point.y = std::min(p.y, min_point.y);
        }
        min_point.x = min_point.x - 10;
        min_point.y = min_point.y - 10;

        cv::Mat eye = cv::Mat(96, 96, CV_8UC3, cv::Scalar(255, 255, 255));
        // eye countour
        for (auto p : eye_contour) {
            cv::circle(eye, cv::Point(p.x-min_point.x, p.y-min_point.y), 2, cv::Scalar(0, 255, 0), -1);
        }
        // iris
        // cv::Rect iris_rect_ = cv::Rect(cv::Point(iris_rect.center.x-iris_rect.size.width/2,iris_rect.center.y-iris_rect.size.height/2),
        //  cv::Size(iris_rect.size.width, iris_rect.size.height));
        // cv::rectangle(eye, iris_rect_,cv::Scalar(0, 255, 0), -1);
        for (auto p:iris_contour) {
            cv::circle(eye, cv::Point(p.x-min_point.x, p.y-min_point.y), 2, cv::Scalar(0, 0, 255), -1);
        }
        // pupils
        cv::circle(eye, cv::Point(pupil.x-min_point.x, pupil.y-min_point.y), 1, cv::Scalar(255, 0 ,255), -1);
        std::string name = "eye_" + std::to_string(count) + ".jpg";
        cv::imwrite(name, eye);
        std::cout << "save"+name << std::endl;
        count++;
    }
#endif

    std::shared_ptr<std::vector<float>> data(new std::vector<float>);
    data->resize(59);
    data->operator[](0) = eye_flag;
    data->operator[](1) = eye_rect.angle;
    data->operator[](2) = eye_rect.center.x;
    data->operator[](3) = eye_rect.center.y;
    data->operator[](4) = eye_rect.size.width;
    data->operator[](5) = eye_rect.size.height;
    data->operator[](6) = iris_flag;
    data->operator[](7) = iris_rect.center.x;
    data->operator[](8) = iris_rect.center.y;
    data->operator[](9) = iris_rect.size.width;
    data->operator[](10) = pupil_flag;
    data->operator[](11) = pupil_rect.center.x;
    data->operator[](12) = pupil_rect.center.y;
    data->operator[](13) = pupil_rect.size.width;
    data->operator[](14) = target_point.x;
    // if (0 == iris_flag) {
    //     data->operator[](14) = 0;
    //     data->operator[](15) = 0;
    // } else {
    data->operator[](14) = target_point.x;
    data->operator[](15) = target_point.y;
    // }
    data->operator[](16) = eye_opening;
    data->operator[](17) = up_down_proportion;

    // 增加眼睛，虹膜，瞳孔的分数和关键点信息输出
    data->operator[](18) = eye_score;
    eye_info.true_eye_score = eye_score;

    for (size_t i = 0; i < eye_contour.size(); i++) {
        data->operator[](19 + 2 * i) = eye_contour[i].x;
        data->operator[](19 + (2 * i + 1)) = eye_contour[i].y;
        eye_info.eye_coutours[i].x = eye_contour[i].x;
        eye_info.eye_coutours[i].y = eye_contour[i].y;
    }

    data->operator[](35) = iris_score;
    eye_info.true_iris_score = iris_score;

    for (size_t i = 0; i < iris_contour.size(); i++) {
        data->operator[](36 + 2 * i) = iris_contour[i].x;
        data->operator[](36 + (2 * i + 1)) = iris_contour[i].y;
        eye_info.iris_coutours[i].x = iris_contour[i].x;
        eye_info.iris_coutours[i].y = iris_contour[i].y;
    }

    data->operator[](52) = pupil_score;
    eye_info.true_pupil_score = pupil_score;

    data->operator[](53) = pupil.x;
    data->operator[](54) = pupil.y;
    eye_info.pupil.x = pupil.x;
    eye_info.pupil.y = pupil.y;

    data->operator[](55) = pupil_to_up_down_dis;
    data->operator[](56) = pupil_to_left_right_dis;

    data->operator[](57) = is_curved;
    data->operator[](58) = curve_score;

    eye_info.is_curve = is_curved;
    eye_info.curve_score = curve_score;
    return data;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_right_eye_landmarks() {
    TX_LOG_DEBUG("ADDAW", "get_driving_right_eye_pose1");
    // auto start_time = std::chrono::system_clock::now();
    auto face_attr = get_driving_face_attr();
    bool is_glasses = face_attr->operator[](0) >= 0.7;
    bool is_righteye = true;
    std::shared_ptr<std::vector<float>> result =
        get_driving_eye_landmarks(getCachedOutput(7), getCachedOutput(8), get_driving_face_angle(),
                                  get_driving_face_keypoint(), is_glasses, is_righteye, r_eyeinfo);
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "右眼睛分割检测模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    return result;
}
std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_left_eye_landmarks() {
    TX_LOG_DEBUG("ADDAW", "get_driving_left_eye_pose1");
    // auto start_time = std::chrono::system_clock::now();
    auto face_attr = get_driving_face_attr();
    bool is_glasses = face_attr->operator[](0) >= 0.7;
    bool is_righteye = false;
    std::shared_ptr<std::vector<float>> result =
        get_driving_eye_landmarks(getCachedOutput(9), getCachedOutput(10), get_driving_face_angle(),
                                  get_driving_face_keypoint(), is_glasses, is_righteye, l_eyeinfo);
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "左眼睛分割检测模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    return result;
}

std::shared_ptr<std::vector<CcObjBBox>> CcDmsProcess::get_phone_cig_bbox() {
    std::shared_ptr<std::vector<CcObjBBox>> output(new std::vector<CcObjBBox>);
    // auto feat = grop->getOutput(11);
    // auto offset_scale = grop->getOutput(12);
    // auto offset_scale_ptr = (float*)offset_scale->data;

    // auto seg = feat->data;
    // int h = feat->shape[2];
    // int w = feat->shape[3];

    // cv::Mat cig_mat = cv::Mat(h, w, CV_8UC1, &seg[1 * h * w]);
    // cv::Mat phone_mat = cv::Mat(h, w, CV_8UC1, &seg[2 * h * w]);
    // std::vector<std::vector<cv::Point>> phone_contours;
    // std::vector<std::vector<cv::Point>> cig_contours;
    // cv::findContours(phone_mat, phone_contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    // cv::findContours(cig_mat, cig_contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    // for (int i = 0; i < phone_contours.size(); ++i) {
    //     cv::Rect rect = cv::boundingRect(cv::Mat(phone_contours[i]));
    //     rect.x = rect.x * offset_scale_ptr[2] + offset_scale_ptr[0];
    //     rect.y = rect.y * offset_scale_ptr[3] + offset_scale_ptr[1];
    //     rect.width = rect.width * offset_scale_ptr[2];
    //     rect.height = rect.height * offset_scale_ptr[3];
    //     CcObjBBox bbox;
    //     bbox.label = 1;
    //     bbox.score = 1;
    //     bbox.bbox = rect;
    //     output->push_back(bbox);
    // }
    // for (int i = 0; i < cig_contours.size(); ++i) {
    //     cv::Rect rect = cv::boundingRect(cv::Mat(cig_contours[i]));
    //     rect.x = rect.x * offset_scale_ptr[2] + offset_scale_ptr[0];
    //     rect.y = rect.y * offset_scale_ptr[3] + offset_scale_ptr[1];
    //     rect.width = rect.width * offset_scale_ptr[2];
    //     rect.height = rect.height * offset_scale_ptr[3];
    //     CcObjBBox bbox;
    //     bbox.label = 2;
    //     bbox.score = 1;
    //     bbox.bbox = rect;
    //     output->push_back(bbox);
    // }

    return output;
}


// 统一的关键点不稳定性评分获取方法
std::shared_ptr<std::vector<KeypointInstabilityInfo>> CcDmsProcess::get_keypoints_instability_scores(
    KeypointType keypoint_type,
    bool sort_by_score,
    const std::vector<int>& exclude_indices) {

    // 根据关键点类型确定缓存输出索引和日志标识
    int cache_index = static_cast<int>(keypoint_type);
    std::string type_name;
    switch (keypoint_type) {
        case KeypointType::FACE:
            type_name = "face";
            break;
        case KeypointType::LEFT_EYE:
            type_name = "left_eye";
            break;
        case KeypointType::RIGHT_EYE:
            type_name = "right_eye";
            break;
        default:
            type_name = "unknown";
            break;
    }

    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_keypoints_instability_scores start (type: %s)", type_name.c_str());
    std::shared_ptr<std::vector<KeypointInstabilityInfo>> ret(new std::vector<KeypointInstabilityInfo>);

    auto stability_output = getCachedOutput(cache_index);
    if (stability_output && stability_output->shape.size() > 0 && stability_output->shape[0] > 0) {
        float* instability_scores = (float*)stability_output->data;
        int num_keypoints = stability_output->shape[0];

        // 创建排除索引的集合，便于快速查找
        std::set<int> exclude_set(exclude_indices.begin(), exclude_indices.end());

        // 收集所有未被排除的关键点信息
        for (int i = 0; i < num_keypoints; i++) {
            // 检查是否需要排除此索引
            if (exclude_set.find(i) == exclude_set.end()) {
                ret->emplace_back(i, instability_scores[i]);
            }
        }

        // 如果需要排序，按不稳定性评分降序排列（最不稳定的在前）
        if (sort_by_score) {
            std::sort(ret->begin(), ret->end(),
                [](const KeypointInstabilityInfo& a, const KeypointInstabilityInfo& b) {
                    return a.score > b.score;  // 降序排列
                });
        }

        // TX_LOG_DEBUG("TX DMS", " Retrieved %d %s keypoints instability info (excluded %d, sorted: %s)",
        //             static_cast<int>(ret->size()), type_name.c_str(), static_cast<int>(exclude_indices.size()),
        //             sort_by_score ? "yes" : "no");
    } else {
        // TX_LOG_DEBUG("TX DMS", " No %s stability output available", type_name.c_str());
    }

    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_keypoints_instability_scores end (type: %s)", type_name.c_str());
    return ret;
}

std::shared_ptr<std::vector<KeypointInstabilityInfo>> CcDmsProcess::get_face_keypoints_instability_scores(
    bool sort_by_score,
    const std::vector<int>& exclude_indices) {
    return get_keypoints_instability_scores(KeypointType::FACE, sort_by_score, exclude_indices);
}

std::shared_ptr<std::vector<KeypointInstabilityInfo>> CcDmsProcess::get_left_eye_keypoints_instability_scores(
    bool sort_by_score,
    const std::vector<int>& exclude_indices) {
    return get_keypoints_instability_scores(KeypointType::LEFT_EYE, sort_by_score, exclude_indices);
}

std::shared_ptr<std::vector<KeypointInstabilityInfo>> CcDmsProcess::get_right_eye_keypoints_instability_scores(
    bool sort_by_score,
    const std::vector<int>& exclude_indices) {
    return get_keypoints_instability_scores(KeypointType::RIGHT_EYE, sort_by_score, exclude_indices);
}

std::shared_ptr<std::vector<EyeRegionInstabilityInfo>> CcDmsProcess::get_eye_regions_instability_status(
    const std::shared_ptr<std::vector<KeypointInstabilityInfo>>& instability_scores,
    float instability_threshold,
    const std::vector<int>& exclude_indices) {

    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_eye_regions_instability_status start");
    std::shared_ptr<std::vector<EyeRegionInstabilityInfo>> ret(new std::vector<EyeRegionInstabilityInfo>);

    if (!instability_scores || instability_scores->empty()) {
        TX_LOG_DEBUG("TX DMS", " No eye instability scores available");
        return ret;
    }

    // 创建关键点索引到评分的映射
    std::map<int, float> keypoint_scores;
    for (const auto& info : *instability_scores) {
        keypoint_scores[info.index] = info.score;
    }

    // 遍历每个眼部区域
    for (const auto& region_pair : EYE_REGION_KEYPOINTS) {
        EyeRegion region = region_pair.first;
        const std::vector<int>& keypoint_indices = region_pair.second;

        int total_points = 0;
        int unstable_points = 0;

        // 统计该区域的关键点不稳定性
        for (int keypoint_idx : keypoint_indices) {
            // 检查是否在排除列表中
            if (std::find(exclude_indices.begin(), exclude_indices.end(), keypoint_idx) != exclude_indices.end()) {
                continue;
            }

            // 检查是否有该关键点的评分数据
            auto score_it = keypoint_scores.find(keypoint_idx);
            if (score_it != keypoint_scores.end()) {
                total_points++;
                if (score_it->second > instability_threshold) {
                    unstable_points++;
                }
            }
        }

        // 计算不稳定比例
        float unstable_ratio = (total_points > 0) ? static_cast<float>(unstable_points) / total_points : 0.0f;

        // 判断区域是否不稳定（一半及以上的点不稳定）
        bool is_region_unstable = unstable_ratio >= 0.5f;

        // 获取区域名称
        std::string region_name = EYE_REGION_NAMES.at(region);

        // 添加到结果中
        ret->emplace_back(region, region_name, total_points, unstable_points, unstable_ratio, is_region_unstable);

        // TX_LOG_DEBUG("TX DMS", " Eye Region %s: %d/%d unstable (%.2f%%), region_unstable: %s",
        //             region_name.c_str(), unstable_points, total_points, unstable_ratio * 100.0f,
        //             is_region_unstable ? "YES" : "NO");
    }

    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_eye_regions_instability_status end");
    return ret;
}

std::shared_ptr<std::vector<float>> CcDmsProcess::get_driving_face_attr() {
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_attr start");
    // auto start_time = std::chrono::system_clock::now();
    std::shared_ptr<std::vector<float>> ret(new std::vector<float>);
    float* face_attr = (float*)getCachedOutput(6)->data;
    ret->push_back(face_attr[0]);
    ret->push_back(face_attr[1]);
    ret->push_back(face_attr[2] * face_attr[0]);
    // auto end_time = std::chrono::system_clock::now();
    // std::chrono::duration<double, std::milli> cost_time = end_time - start_time;
    // std::cout << "人脸属性检测模块耗时间为：" << cost_time.count() << " ms" << std::endl;
    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_driving_face_attr end");
    return ret;
}

std::shared_ptr<std::vector<RegionInstabilityInfo>> CcDmsProcess::get_facial_regions_instability_status(
    const std::shared_ptr<std::vector<KeypointInstabilityInfo>>& instability_scores,
    float instability_threshold,
    const std::vector<int>& exclude_indices) {

    TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_facial_regions_instability_status start");
    std::shared_ptr<std::vector<RegionInstabilityInfo>> ret(new std::vector<RegionInstabilityInfo>);

    if (!instability_scores || instability_scores->empty()) {
        TX_LOG_DEBUG("TX DMS", " No instability scores available");
        return ret;
    }

    // 创建关键点索引到评分的映射
    std::map<int, float> keypoint_scores;
    for (const auto& info : *instability_scores) {
        keypoint_scores[info.index] = info.score;
    }

    // 遍历每个面部区域
    for (const auto& region_pair : REGION_KEYPOINTS) {
        FacialRegion region = region_pair.first;
        const std::vector<int>& keypoint_indices = region_pair.second;

        int total_points = 0;
        int unstable_points = 0;

        // 统计该区域的关键点不稳定性
        for (int keypoint_idx : keypoint_indices) {
            // 检查是否在排除列表中
            if (std::find(exclude_indices.begin(), exclude_indices.end(), keypoint_idx) != exclude_indices.end()) {
                continue;
            }

            // 检查是否有该关键点的评分数据
            auto score_it = keypoint_scores.find(keypoint_idx);
            if (score_it != keypoint_scores.end()) {
                total_points++;
                if (score_it->second > instability_threshold) {
                    unstable_points++;
                }
            }
        }

        // 计算不稳定比例
        float unstable_ratio = (total_points > 0) ? static_cast<float>(unstable_points) / total_points : 0.0f;

        // 判断区域是否不稳定（一半及以上的点不稳定）
        bool is_region_unstable = unstable_ratio >= 0.5f;

        // 获取区域名称
        std::string region_name = REGION_NAMES.at(region);

        // 添加到结果中
        ret->emplace_back(region, region_name, total_points, unstable_points, unstable_ratio, is_region_unstable);

        // TX_LOG_DEBUG("TX DMS", " Region %s: %d/%d unstable (%.2f%%), region_unstable: %s",
        //             region_name.c_str(), unstable_points, total_points, unstable_ratio * 100.0f,
        //             is_region_unstable ? "YES" : "NO");
    }

    // TX_LOG_DEBUG("TX DMS", " CcDmsProcess::get_facial_regions_instability_status end");
    return ret;
}


void CcDmsProcess::get_eye_info_(EyeInfos& eyeinfos) {
    eyeinfos.left_eye = l_eyeinfo;
    eyeinfos.right_eye = r_eyeinfo;
    r_eyeinfo = {0};
    l_eyeinfo = {0};
}

int CcDmsProcess::updateCarInfo(const TXCarInfo* carInfo) {
    if (carInfo->mask & TX_CAR_INFO_MASK_SPEED) {
        car_info_.speed = carInfo->speed;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_STEER_WHL_SNSR) {
        car_info_.steer_whl_snsr_rad = carInfo->steer_whl_snsr_rad;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_STEER_WHL_SNSR_SPEED) {
        car_info_.steer_whl_snsr_rad_spd = carInfo->steer_whl_snsr_rad_spd;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_GEARPOSITION) {
        car_info_.gear = carInfo->gear;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_LDW) {
        car_info_.ldw = carInfo->ldw;
    }
    if (carInfo->mask & TX_CAR_INFO_MASK_TURN_LIGHT) {
        car_info_.turn_light = carInfo->turn_light;
    }
    if (carInfo->mask & TX_CAR_CAN_FAULT) {
        car_info_.can_fault = carInfo->can_fault;
    }
    if (carInfo->mask & TX_CAR_CAMERA_FAULT) {
        car_info_.camera_fault = carInfo->camera_fault;
    }
    if (carInfo->mask & TX_CAR_DRIVER_DOOR) {
        car_info_.driver_door_status = carInfo->driver_door_status;
    }
    if (carInfo->mask & TX_CAR_DRIVER_SEAT) {
        car_info_.driver_seat_status = carInfo->driver_seat_status;
    }
    return 0;
}

std::string CcDmsProcess::GetDistractReason(TXCameraStatus camera_status) {
    return dms_process_.GetDistractReason(camera_status);
}

std::string CcDmsProcess::GetDistractParamers() {
    return dms_process_.GetDistractParamers();
}

void CcDmsProcess::GetTiredInfo(tx_tired& tired_info) {
    return dms_process_.GetTiredInfo(tired_info);
}

void CcDmsProcess::GetDistractionInfo(internal_analysis_distraction_info& info) {
    return dms_process_.GetDistractionInfo(info);
}

int CcDmsProcess::GetRightLeftEyeThr(float& left_eye_thr, float& right_eye_thr) {
    return dms_process_.GetRightLeftEyeThr(left_eye_thr, right_eye_thr);
}

//以下函数用于保存图像及结果json到nfs挂载路径下

void CcDmsProcess::SaveResultToWeb(TXDmsResult& dms_result, const TXImageInfo* image) {
    if (tx_media_server_push_image_and_message == NULL || tx_media_server_get_status == NULL) {
        return;
    }
    if (tx_media_server_get_status()) {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        std::string ts = std::to_string(now_ts);

        Json::Value root;
        Json::Value dms_result_json;
        Json::Value warnInfo_json;
        Json::Value tired_json;
        Json::Value distraction_json;
        dms_result_json["result_frame_id"] = dms_result.result_frame_id;
        dms_result_json["camera_status"] = dms_result.camera_status;
        dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
        dms_result_json["distraction_type"] = dms_result.distraction_status;
        dms_result_json["calibrate_status"] = dms_result.calibrate_status;
        dms_result_json["system_status"] = dms_result.system_status;
        dms_result_json["car_speed"] = car_info_.speed;
        dms_result_json["car_gear"] = car_info_.gear;
        dms_result_json["car_steer_whl_snsr_rad"] = car_info_.steer_whl_snsr_rad;
        dms_result_json["turn_light"] = car_info_.turn_light;
        dms_result_json["door_status"] = car_info_.driver_door_status;
        dms_result_json["seat_status"] = car_info_.driver_seat_status;
        dms_result_json["distraction_params"] = dms_process_.GetDistractParamers();
        dms_result_json["distraction_reason"] =
            dms_process_.GetDistractReason(dms_result.camera_status);
        float left_eye_thr, right_eye_thr;
        GetRightLeftEyeThr(left_eye_thr, right_eye_thr);
        dms_result_json["left_up_down_proportion"] = left_eye_thr;
        dms_result_json["right_up_down_proportion"] = right_eye_thr;
        {
            Json::Value face_info;
            face_info["score"] = dms_result.face_info.score;
            face_info["xmin"] = dms_result.face_info.xmin / 2;
            face_info["ymin"] = dms_result.face_info.ymin / 2;
            face_info["xmax"] = dms_result.face_info.xmax / 2;
            face_info["ymax"] = dms_result.face_info.ymax / 2;
            face_info["yaw"] = dms_result.face_info.head_yaw;
            face_info["pitch"] = dms_result.face_info.head_pitch;
            face_info["roll"] = dms_result.face_info.head_roll;
            face_info["isMask"] = dms_result.face_info.isMask;
            face_info["isGlass"] = dms_result.face_info.isGlass;
            face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
            face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
            face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
            face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
            Json::Value landmarks(Json::arrayValue);
            for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
                Json::Value point;
                point["x"] = dms_result.face_info.landmarks[i].x / 2;
                point["y"] = dms_result.face_info.landmarks[i].y / 2;
                landmarks[i] = point;
            }

            {
                Json::Value right_eye_landmark;
                right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
                right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
                right_eye_landmark["eye_center"] = Json::Value();
                right_eye_landmark["eye_center"]["x"] =
                    dms_result.face_info.right_eye_landmark.eye_center.x / 2;
                right_eye_landmark["eye_center"]["y"] =
                    dms_result.face_info.right_eye_landmark.eye_center.y / 2;
                right_eye_landmark["eye_size"] = Json::Value();
                right_eye_landmark["eye_size"]["width"] =
                    dms_result.face_info.right_eye_landmark.eye_size.width / 2;
                right_eye_landmark["eye_size"]["height"] =
                    dms_result.face_info.right_eye_landmark.eye_size.height / 2;
                right_eye_landmark["iris_score"] =
                    dms_result.face_info.right_eye_landmark.iris_score;
                right_eye_landmark["iris_center"] = Json::Value();
                right_eye_landmark["iris_center"]["x"] =
                    dms_result.face_info.right_eye_landmark.iris_center.x / 2;
                right_eye_landmark["iris_center"]["y"] =
                    dms_result.face_info.right_eye_landmark.iris_center.y / 2;
                right_eye_landmark["iris_radius"] =
                    dms_result.face_info.right_eye_landmark.iris_radius / 2;
                right_eye_landmark["pupil_score"] =
                    dms_result.face_info.right_eye_landmark.pupil_score;
                right_eye_landmark["pupil_center"] = Json::Value();
                right_eye_landmark["pupil_center"]["x"] =
                    dms_result.face_info.right_eye_landmark.pupil_center.x / 2;
                right_eye_landmark["pupil_center"]["y"] =
                    dms_result.face_info.right_eye_landmark.pupil_center.y / 2;
                right_eye_landmark["pupil_radius"] =
                    dms_result.face_info.right_eye_landmark.pupil_radius / 2;
                right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
                right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
                right_eye_landmark["opening"] = dms_result.face_info.right_eye_landmark.opening;

                right_eye_landmark["true_eye_score"] = r_eyeinfo.true_eye_score;
                right_eye_landmark["eye_coutours"] = Json::Value();
                Json::Value reye_coutours(Json::arrayValue);
                for (int i = 0; i < 8; i++) {
                    Json::Value point;
                    point["x"] = r_eyeinfo.eye_coutours[i].x / 2;
                    point["y"] = r_eyeinfo.eye_coutours[i].y / 2;
                    reye_coutours[i] = point;
                }
                right_eye_landmark["eye_coutours"] = reye_coutours;

                right_eye_landmark["true_iris_score"] = r_eyeinfo.true_iris_score;
                right_eye_landmark["iris_coutours"] = Json::Value();
                Json::Value riris_coutours(Json::arrayValue);
                for (int i = 0; i < 8; i++) {
                    Json::Value point;
                    point["x"] = r_eyeinfo.iris_coutours[i].x / 2;
                    point["y"] = r_eyeinfo.iris_coutours[i].y / 2;
                    riris_coutours[i] = point;
                }
                right_eye_landmark["iris_coutours"] = riris_coutours;

                right_eye_landmark["true_pupil_score"] = r_eyeinfo.true_pupil_score;
                right_eye_landmark["pupil"] = Json::Value();
                right_eye_landmark["pupil"]["x"] = r_eyeinfo.pupil.x / 2;
                right_eye_landmark["pupil"]["y"] = r_eyeinfo.pupil.y / 2;
                right_eye_landmark["curve_score"] = r_eyeinfo.curve_score;
                right_eye_landmark["is_curve"] = r_eyeinfo.is_curve;
                face_info["right_eye_landmark"] = right_eye_landmark;

                Json::Value left_eye_landmark;
                left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
                left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
                left_eye_landmark["eye_center"] = Json::Value();
                left_eye_landmark["eye_center"]["x"] =
                    dms_result.face_info.left_eye_landmark.eye_center.x / 2;
                left_eye_landmark["eye_center"]["y"] =
                    dms_result.face_info.left_eye_landmark.eye_center.y / 2;
                left_eye_landmark["eye_size"] = Json::Value();
                left_eye_landmark["eye_size"]["width"] =
                    dms_result.face_info.left_eye_landmark.eye_size.width / 2;
                left_eye_landmark["eye_size"]["height"] =
                    dms_result.face_info.left_eye_landmark.eye_size.height / 2;
                left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
                left_eye_landmark["iris_center"] = Json::Value();
                left_eye_landmark["iris_center"]["x"] =
                    dms_result.face_info.left_eye_landmark.iris_center.x / 2;
                left_eye_landmark["iris_center"]["y"] =
                    dms_result.face_info.left_eye_landmark.iris_center.y / 2;
                left_eye_landmark["iris_radius"] =
                    dms_result.face_info.left_eye_landmark.iris_radius / 2;
                left_eye_landmark["pupil_score"] =
                    dms_result.face_info.left_eye_landmark.pupil_score;
                left_eye_landmark["pupil_center"] = Json::Value();
                left_eye_landmark["pupil_center"]["x"] =
                    dms_result.face_info.left_eye_landmark.pupil_center.x / 2;
                left_eye_landmark["pupil_center"]["y"] =
                    dms_result.face_info.left_eye_landmark.pupil_center.y / 2;
                left_eye_landmark["pupil_radius"] =
                    dms_result.face_info.left_eye_landmark.pupil_radius / 2;
                left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
                left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
                left_eye_landmark["opening"] = dms_result.face_info.left_eye_landmark.opening;

                left_eye_landmark["true_eye_score"] = l_eyeinfo.true_eye_score;
                left_eye_landmark["eye_coutours"] = Json::Value();
                Json::Value leye_coutours(Json::arrayValue);
                for (int i = 0; i < 8; i++) {
                    Json::Value point;
                    point["x"] = l_eyeinfo.eye_coutours[i].x / 2;
                    point["y"] = l_eyeinfo.eye_coutours[i].y / 2;
                    leye_coutours[i] = point;
                }
                left_eye_landmark["eye_coutours"] = leye_coutours;

                left_eye_landmark["true_iris_score"] = l_eyeinfo.true_iris_score;
                left_eye_landmark["iris_coutours"] = Json::Value();
                Json::Value liris_coutours(Json::arrayValue);
                for (int i = 0; i < 8; i++) {
                    Json::Value point;
                    point["x"] = l_eyeinfo.iris_coutours[i].x / 2;
                    point["y"] = l_eyeinfo.iris_coutours[i].y / 2;
                    liris_coutours[i] = point;
                }
                left_eye_landmark["iris_coutours"] = liris_coutours;

                left_eye_landmark["true_pupil_score"] = l_eyeinfo.true_pupil_score;
                left_eye_landmark["pupil"] = Json::Value();
                left_eye_landmark["pupil"]["x"] = l_eyeinfo.pupil.x / 2;
                left_eye_landmark["pupil"]["y"] = l_eyeinfo.pupil.y / 2;
                left_eye_landmark["curve_score"] = l_eyeinfo.curve_score;
                left_eye_landmark["is_curve"] = l_eyeinfo.is_curve;
                face_info["left_eye_landmark"] = left_eye_landmark;
            }
            face_info["landmarks"] = landmarks;
            dms_result_json["face_info"] = face_info;

            r_eyeinfo = {0};
            l_eyeinfo = {0};
        }
        {
            warnInfo_json["eye1_60s_10p"] = dms_result.face_info.warnInfo.eye1_60s_10p;
            warnInfo_json["mouth1_120s_2"] = dms_result.face_info.warnInfo.mouth1_120s_2;
            warnInfo_json["mouth1_1l_1"] = dms_result.face_info.warnInfo.mouth1_1l_1;
            warnInfo_json["eye2_20s_1f5"] = dms_result.face_info.warnInfo.eye2_20s_1f5;
            warnInfo_json["eye2_2_0f75"] = dms_result.face_info.warnInfo.eye2_2_0f75;
            warnInfo_json["eye2_1l_0f75"] = dms_result.face_info.warnInfo.eye2_1l_0f75;
            warnInfo_json["eye2_60s_12p"] = dms_result.face_info.warnInfo.eye2_60s_12p;
            warnInfo_json["mouth2_1l_2"] = dms_result.face_info.warnInfo.mouth2_1l_2;
            warnInfo_json["mouth2_120s_3"] = dms_result.face_info.warnInfo.mouth2_120s_3;
            warnInfo_json["eye3_20s_2f4"] = dms_result.face_info.warnInfo.eye3_20s_2f4;
            warnInfo_json["eye3_2_1f2"] = dms_result.face_info.warnInfo.eye3_2_1f2;
            warnInfo_json["eye3_1l_1f2"] = dms_result.face_info.warnInfo.eye3_1l_1f2;
            warnInfo_json["repetition"] = dms_result.face_info.warnInfo.repetition;
        }
        {
            tx_tired temp = {0};
            GetTiredInfo(temp);
            tired_json["total_eye_count"] = temp.total_eye_count;
            tired_json["close_eye_ratio"] = temp.close_eye_ratio;
            tired_json["close_eye_count"] = int(temp.close_eye_count);
            tired_json["close_eye_duration_ms"] = temp.close_eye_duration_ms;
            tired_json["close_eye_start_end_index"] = (temp.close_eye_start_end_index);
            tired_json["total_mouth_count"] = int(temp.total_mouth_count);
            tired_json["yawn_count"] = temp.yawn_count;
            tired_json["open_mouth_duration_ms"] = (temp.open_mouth_duration_ms);
            tired_json["open_mouth_start_end_index"] = (temp.open_mouth_start_end_index);
        }
        {
            internal_analysis_distraction_info temp = {0};
            GetDistractionInfo(temp);
            distraction_json["distraction_continue_percent"] = temp.distraction_continue_percent;
            distraction_json["distraction_continue_time"] = int(temp.distraction_continue_time);
            distraction_json["distraction_sum_time"] = int(temp.distraction_sum_time);
            distraction_json["distraction_front_continue_time"] =
                int(temp.distraction_front_continue_time);
            distraction_json["time_gap"] = int(temp.time_gap);
        }

        root["ts"] = ts;
        root["dms_result"] = dms_result_json;
        root["warnInfo"] = warnInfo_json;
        root["tiredInfo"] = tired_json;
        root["distraction_info"] = distraction_json;
        root["sdk_version"] = GetBuildType() + std::string("_") + TXDmsGetRealVersion();

        TXMediaServerImageInfo media_image;
        media_image.data = image->data;
        media_image.dataLen = image->dataLen;
        media_image.dataType = (TXMediaServerImageFormat)image->dataType;
        media_image.height = image->height;
        media_image.stride = image->stride;
        media_image.width = image->width;
        std::string message = Json::FastWriter().write(root);
        tx_media_server_push_image_and_message(&media_image, (char*)message.data(), message.size(),
                                               2);
    }
}

bool tongxing::CcDmsProcess::validateAndCorrectPupil(float pupil_x, float pupil_y,
                                                   const std::vector<cv::Point2f>& iris_contour,
                                                   cv::Point2f& pupil, float& pupil_flag) {
    if (pupil_x > 0 && pupil_y > 0) {
        cv::Point2f tmp_pupil(pupil_x, pupil_y);
        double distance = pointPolygonTest(iris_contour, tmp_pupil, true);
        if (distance > 0) {
            pupil = tmp_pupil;
            pupil_flag = 3;
            return true;
        }
    }
    return false;
}

// 一次获取避免多次调用grop的getOutput
std::shared_ptr<NumArray> CcDmsProcess::getCachedOutput(int index) {
    auto it = output_cache_.find(index);
    if (it != output_cache_.end()) {
        return it->second;
    }
    // 如果缓存中没有，直接从grop获取（兜底机制）
    return grop->getOutput(index);
}

void CcDmsProcess::preloadCache() {
    // 清空旧缓存
    output_cache_.clear();
    
    // 立即重新加载所有需要的输出索引
    std::vector<int> required_indices = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14};

    for (int idx : required_indices) {
        if (idx < grop->getOutputNum()) {
            output_cache_[idx] = grop->getOutput(idx);
        }
    }
    
    cache_valid_ = true;
    cache_frame_id_ = frame_id;


}

// 获取眼睛关键点
std::shared_ptr<std::vector<cv::Point2f>> CcDmsProcess::get_driving_eye_keypoint() {
    TX_LOG_DEBUG("TX DMS", "CcDmsProcess::get_driving_eye_keypoint start");
    std::shared_ptr<std::vector<cv::Point2f>> ret(new std::vector<cv::Point2f>);
    
    // 处理右眼数据 - getCachedOutput(7)
    auto right_eye_feat = getCachedOutput(7);
    
    if (right_eye_feat && right_eye_feat->data) {
        float* right_eye_data = (float*)right_eye_feat->data;
        // 根据数据格式添加右眼关键点
        int num_points = right_eye_feat->shape[1]; //17
        int stride = right_eye_feat->shape[2]; //3
        // std::cout << "right_eye_feat->shape[1]:" << right_eye_feat->shape[1] << " right_eye_feat->shape[2]:" << right_eye_feat->shape[2] << std::endl;
        for (int i = 0; i < num_points; i++) {
            float x = right_eye_data[stride * i + 1];
            float y = right_eye_data[stride * i + 2];
            ret->push_back(cv::Point2f(x, y));
        }
    }
    
    // 处理左眼数据 - getCachedOutput(9)
    auto left_eye_feat = getCachedOutput(9);
    
    if (left_eye_feat && left_eye_feat->data) {
        float* left_eye_data = (float*)left_eye_feat->data;
        // 根据数据格式添加左眼关键点
        int num_points = left_eye_feat->shape[1]; //17
        int stride = left_eye_feat->shape[2]; //3
        // std::cout << "left_eye_feat->shape[1]:" << left_eye_feat->shape[1] << " left_eye_feat->shape[2]:" << left_eye_feat->shape[2] << std::endl;
        for (int i = 0; i < num_points; i++) {
            float x = left_eye_data[stride * i + 1];
            float y = left_eye_data[stride * i + 2];
            ret->push_back(cv::Point2f(x, y));
        }
    }
        
    TX_LOG_DEBUG("TX DMS", "CcDmsProcess::get_driving_eye_keypoint end, total points: %d", (int)ret->size());
    return ret;
}

}  // namespace tongxing
