#include "dms_calculate_warning_byd.h"
#include "CalmCarLog.h"

namespace tongxing {

DrowsinessWarn::DrowsinessWarn(int car_speed,
                               int eye_timewindow,
                               int mouth_timewindow,
                               int ldw_timewindow) {
    car_speed_ = car_speed;
    eye_timewindow_ = eye_timewindow;
    mouth_timewindow_ = mouth_timewindow;
    ldw_timewindow_ = ldw_timewindow;

    mouth_result.clear();
    eye_60s_result.clear();

    eye_temp_result.clear();
    mouth_temp_result.clear();

    tired_info_ = {0};  //详细占比
}

DrowsinessWarn::~DrowsinessWarn() {}

void DrowsinessWarn::Update(const Warn_Info& info) {
    std::pair<long, bool> eye_pair;
    eye_pair.first = info.time_input;
    eye_pair.second = info.eye_close_status;
    eye_60s_result.emplace_back(eye_pair);

    std::pair<long, size_t> mouth_pair;
    mouth_pair.first = info.time_input;
    mouth_pair.second = info.mouth_open_status; // 外层需要使用双端阈值，来区分不止2种状态
    mouth_result.emplace_back(mouth_pair);

    if ((mouth_result.size() >= 2)) {
        if ((mouth_result.back().first - mouth_result.front().first) > 120000) {
            mouth_result.pop_front();  //移除队列最前面的数据
        }
    }
    if (eye_60s_result.size() >= 2) {
        if ((eye_60s_result.back().first - eye_60s_result.front().first) > 60000) {
            eye_60s_result.pop_front();
        }
    }

    cache_time = info.time_input;
    temp_warn = info;

    return;
}

//这里计算各个报警状态
//单位时间内闭眼累计时长(占百分比)
// 获取眼睛时间窗占比
bool DrowsinessWarn::GetEyeCloseThreshold(double thres) {
    int total = eye_60s_result.size();
    int count = 0;
    int single_close_count = 0;
    double ratio = 0.0f;

    if (total < 600)
        total = 600;

    long last_close_time = 0;
    bool is_continue_closed = false;
    for (const auto& v : eye_60s_result) {  //过滤快速的眨眼导致的疲劳误报
        if (v.second == true) {
            if (!is_continue_closed)
                last_close_time = v.first;
            is_continue_closed = true;
            single_close_count++;
        } else {
            if (v.first - last_close_time >= 200 && last_close_time != 0)
                count += single_close_count;

            // if (last_close_time!=0)
            //     printf("\033[33m --hjh-- file:dms_calculate_warning_byd.cpp line:%d info:conti_time:%ld \033[0m \n ",__LINE__, v.first-last_close_time);
            single_close_count = 0;
            last_close_time = 0;
            is_continue_closed = false;
        }
    }

    ratio = (count * 1.0 / total);
    tired_info_.close_eye_ratio = ratio;

    return ratio >= thres;
}

//单位时间内闭眼累计时长(占百分比)--改为次数9（眨眼0.2-0.75s）
bool DrowsinessWarn::GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                                          const int& count,
                                          long interval_start_ms,
                                          long interval_end_ms) {
    std::deque<std::pair<long, bool>> temp_deque;
    temp_deque.clear();
    long cost_time = 0;
    int valid_count = 0;  //有效个数
    for (const auto& v : eye_deque) {
        if (v.second == true) {
            temp_deque.emplace_back(v);
        } else {
            if (temp_deque.size() >= 2) {
                cost_time = temp_deque.back().first - temp_deque.front().first;  //单位：毫秒
                if (cost_time >= interval_start_ms && cost_time < interval_end_ms) {
                    valid_count++;
                }
            }
            cost_time = 0;
            temp_deque.clear();
        }
    }

    return valid_count >= count;
}

//单位时间内连续闭眼
bool DrowsinessWarn::GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                                          long interval_start_ms,
                                          long interval_end_ms) {
    std::deque<std::pair<long, bool>> temp_deque;
    temp_deque.clear();
    long cost_time = 0;
    int valid_count = 0;  //有效个数
    for (const auto& v : eye_deque) {
        if (v.second == true) {
            temp_deque.emplace_back(v);
        } else {
            if (temp_deque.size() >= 2) {
                cost_time = temp_deque.back().first - temp_deque.front().first;  //单位：毫秒
                if (cost_time >= interval_start_ms && cost_time < interval_end_ms) {
                    return true;
                }
            }
            cost_time = 0;
            temp_deque.clear();
        }
    }

    return false;
}

//闭眼次数+没有单次闭眼时长限制
bool DrowsinessWarn::GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                                          const int& count,
                                          long interval_start_ms) {
    int valid_count = 0;
    int consecutive_eye_open_count = 0;
    int consecutive_eye_close_count = 0;
    int total_count = 0;
    int eye_close_count = 0;
    bool accumulate_flag = false;  // 是否累积标志
    long start_time = 0, end_time = 0;
    const int EYE_REDURANCY_FRAME = 2;  // 冗余帧数
    const int EYE_CLOSE_FRAME = 3;      // 连续闭眼帧数
    const float EYE_CLOSE_THRESHOLD = 0.9f;
    int eye_index = 0;
    int eye_start_index = 0;
    int eye_end_index = 0;

    // 遍历眼睛状态队列
    for (const auto& v : eye_deque) {
        bool is_eye_close = v.second;  // 是否闭眼
        long cur_time = v.first;
        if (is_eye_close) {
            consecutive_eye_open_count = 0;  // 重置睁眼计数
            consecutive_eye_close_count++;

            // 记录闭眼开始时间
            if (consecutive_eye_close_count == 1 && !accumulate_flag) {
                start_time = cur_time;
                eye_start_index = eye_index;
            }
            // 达到连续闭眼帧数
            if (consecutive_eye_close_count >= EYE_CLOSE_FRAME && !accumulate_flag) {
                accumulate_flag = true;
                total_count += consecutive_eye_close_count - 1;
                eye_close_count += consecutive_eye_close_count - 1;
            }

        } else {
            consecutive_eye_close_count = 0;
            consecutive_eye_open_count++;
        }

        if (accumulate_flag) {
            end_time = cur_time;
            long cost_time = end_time - start_time;
            if (is_eye_close)
                eye_close_count++;
            total_count++;

            float close_ratio = 0.0f;

            // 满足条件时记录有效闭眼周期
            if (consecutive_eye_open_count <= EYE_REDURANCY_FRAME &&
                cost_time >= (interval_start_ms - 100)) {
                if (total_count > 0) {
                    close_ratio = static_cast<float>(eye_close_count) / total_count;
                    // 小概率偶发性bug实在无法捕捉到，先增加个正确性判断补丁(在10fps情况下)
                    if (start_time == 0 ||
                        eye_close_count <
                            ((interval_start_ms - 100) / 100) * EYE_CLOSE_THRESHOLD * 0.8) {
                        accumulate_flag = false;
                        close_ratio = 0.0f;
                    }
                    if (close_ratio >= EYE_CLOSE_THRESHOLD) {
                        valid_count++;
                        accumulate_flag = false;

                        // 可视化信息输出
                        tired_info_.close_eye_count = valid_count;
                        eye_end_index = eye_index + 1;
                        if (valid_count == 1) {
                            tired_info_.close_eye_duration_ms = std::to_string(cost_time);
                            tired_info_.close_eye_start_end_index =
                                std::to_string(eye_start_index) + "-" +
                                std::to_string(eye_end_index);
                        } else {
                            tired_info_.close_eye_duration_ms += ";" + std::to_string(cost_time);
                            tired_info_.close_eye_start_end_index += ";" +
                                std::to_string(eye_start_index) + "-" +
                                std::to_string(eye_end_index);
                        }
                    }
                }

            } else if (consecutive_eye_open_count > EYE_REDURANCY_FRAME) {
                accumulate_flag = false;
            }

            // if (/*interval_start_ms == 1200 || */interval_start_ms == 2400)
            //     std::cout << " interval_start_ms:" << interval_start_ms  << " count:" <<  count << " cost_time:" << cost_time
            //     << " consecutive_eye_close_count:" << consecutive_eye_close_count <<" consecutive_eye_open_count:" <<
            //     consecutive_eye_open_count << " valid_count:" << valid_count <<
            //     " accumulate_flag:" << accumulate_flag << " close_ratio:" << close_ratio << " eye_close_count:" << eye_close_count <<
            //     " total_count:" << total_count << std::endl;
            // 重置计数器
            if (!accumulate_flag) {
                total_count = 0;
                eye_close_count = 0;
                consecutive_eye_open_count = 0;
                consecutive_eye_close_count = 0;
                start_time = 0;
                end_time = 0;
            }
        }
        eye_index++;

        // // 如果已经达到所需的有效闭眼周期，提前退出
        // if (valid_count >= count) {
        //     return true;
        // }
    }

    return valid_count >= count;
}

//单位时间内打哈欠
bool DrowsinessWarn::GetMouthCount(std::deque<std::pair<long, size_t>>& mouth_deque,
                                   const int& count) {
    int valid_yawn = 0;                     // 有效哈欠个数
    int consecutive_mouth_open_count = 0;   // 连续张嘴的帧数
    int consecutive_mouth_close_count = 0;  // 连续闭嘴的帧数
    bool accumulate_flag = false;           // 是否累计标志
    long start_time = 0, end_time = 0;
    const int MOUTH_OPEN_FRAME = 3;        // 连续张嘴帧数
    const int MOUTH_REDUNDANCY_FRAME = 2;  // 冗余帧数
    const long MIN_YAWN_TIME = 2000;       // 最小有效哈欠时间
    const long MAX_YAWN_TIME = 8000;       // 最大有效哈欠时间
    int mouth_index = 0;
    int mouth_start_index = 0;
    int mouth_end_index = 0;

    // 遍历嘴巴状态队列
    for (const auto& v : mouth_deque) {
        size_t yawn_status = v.second;  // 是否张嘴
        long cur_time = v.first;

        if (yawn_status == 1) { // 0: 闭嘴;1：张嘴;2：张嘴到闭嘴之间的中间态
            consecutive_mouth_close_count = 0;  // 重置非张嘴计数
            consecutive_mouth_open_count++;

            // 记录第一次张嘴时间
            if (consecutive_mouth_open_count == 1 && !accumulate_flag) {
                start_time = cur_time;
                mouth_start_index = mouth_index;
            }

            // 达到连续张嘴帧数
            if (consecutive_mouth_open_count >= MOUTH_OPEN_FRAME) {
                accumulate_flag = true;
            }
        } else if (yawn_status == 0 && accumulate_flag) {
            consecutive_mouth_open_count = 0;
            consecutive_mouth_close_count++;
            end_time = cur_time;
            long cost_time = end_time - start_time;

            if (consecutive_mouth_close_count <= MOUTH_REDUNDANCY_FRAME &&
                (cost_time >= MIN_YAWN_TIME && cost_time <= MAX_YAWN_TIME)) {
                valid_yawn++;
                accumulate_flag = false;

                // 可视化信息输出
                tired_info_.yawn_count = valid_yawn;
                mouth_end_index = mouth_index + 1;
                if (valid_yawn == 1) {
                    tired_info_.open_mouth_duration_ms = std::to_string(cost_time);
                    tired_info_.open_mouth_start_end_index =
                        std::to_string(mouth_start_index) + "-" + std::to_string(mouth_end_index);
                } else {
                    tired_info_.open_mouth_duration_ms += ";" + std::to_string(cost_time);
                    tired_info_.open_mouth_start_end_index += ";" +
                        std::to_string(mouth_start_index) + "-" + std::to_string(mouth_end_index);
                }

            } else if (consecutive_mouth_close_count > MOUTH_REDUNDANCY_FRAME) {
                accumulate_flag = false;
            }

            // 重置计数器
            if (!accumulate_flag) {
                consecutive_mouth_close_count = 0;
                start_time = 0;
                end_time = 0;
            }
        } else {
            consecutive_mouth_open_count = 0;
        }
        mouth_index++;
    }

    // 返回是否满足哈欠数量
    return valid_yawn >= count;
}

void DrowsinessWarn::Reset() {
    time_2min_flag = false;
    time_2min_arrived = false;
    time_60s_flag = false;
    time_60s_arrived = false;
    ok_flag = false;
    warn_no_response_flag = false;

    eye_temp_result.clear();
    mouth_temp_result.clear();
    history_flag = false;

    mouth_result.clear();

    eye_60s_result.clear();
    alarm_start_time = cache_time;

    warn_count = 0;
    last_warning_type.clear();

    histor_warn_type = NORMAL;
    his_warn_class = NONE_RELATED;
    tired_info_ = {0};

    return;
}

void DrowsinessWarn::Cleareye() {
    const size_t removeCount = 30;
    size_t total_size = eye_60s_result.size();
    for (size_t i = 0; i < removeCount && total_size >= removeCount; ++i) {
        eye_60s_result[total_size - i - 1].second = false;  //清除看某些分心点位导致的闭眼状态累计
    }
}

static WarningClass CurWarnType(const std::string& cur_type) {
    int index = 0;
    std::vector<std::string> eye_types = {"light_60_9_eye",      "medium_60_10_eye",
                                          "medium_20_2eye_0_75", "medium_eye_close_continue",
                                          "heavy_20_2eye_1_2",   "heavy_20_eye_2_4"};
    std::vector<std::string> mouth_types = {"light_60_2mouth", "medium_60_3mouth", "mouth3_120s_4"};

    if (std::find(eye_types.begin(), eye_types.end(), cur_type) != eye_types.end()) {
        index = 1;
    } else if (std::find(mouth_types.begin(), mouth_types.end(), cur_type) != mouth_types.end()) {
        index = 2;
    }

    return (WarningClass)index;
}

WarningType DrowsinessWarn::GetWarnStatus(TXWarnInfo& warn_info) {
    WarningType status = NORMAL;
    WarningClass warn_class = NONE_RELATED;
    tired_info_.total_eye_count = eye_60s_result.size();
    tired_info_.total_mouth_count = mouth_result.size();
    do {
        bool light_60_9_eye = false, light_60_2mouth = false, medium_60_10_eye = false,
             medium_60_3mouth = false, medium_20_2eye_0_75 = false,
             medium_eye_close_continue = false, heavy_20_2eye_1_2 = false, heavy_20_eye_2_4 = false;
        //记录本次报警的类型
        std::string warning_type = "";

        // 1.先等级仲裁，2，再类型仲裁 (如果中间跳了级，只能说明你等级之间区分做的不好，而不是我的问题)
        //进行类型仲裁时，因为上一个类型的flag还未置下去，所以在原来逻辑下有的时候会抑制到同等级的不同类型。
        if (histor_warn_type <= DROWSINESS_LEVEL_HEAVEY) {
            heavy_20_2eye_1_2 = warn_info.eye3_2_1f2 =
                GetEyeCloseAddUpTime(eye_60s_result, 2, 1200);
            heavy_20_eye_2_4 = warn_info.eye3_20s_2f4 =
                GetEyeCloseAddUpTime(eye_60s_result, 1, 2400);
            if (heavy_20_2eye_1_2 || heavy_20_eye_2_4)
                status = DROWSINESS_LEVEL_HEAVEY;

            if (heavy_20_2eye_1_2)
                warning_type = "heavy_20_2eye_1_2";
            else if (heavy_20_eye_2_4)
                warning_type = "heavy_20_eye_2_4";
        }
        if (status == NORMAL && histor_warn_type <= DROWSINESS_LEVEL_MEDIUM) {
            if (histor_warn_type < DROWSINESS_LEVEL_MEDIUM ||
                (histor_warn_type == DROWSINESS_LEVEL_MEDIUM && his_warn_class != EYES_RELATED)) {
                medium_60_10_eye = warn_info.eye2_60s_12p = GetEyeCloseThreshold(0.15);
                medium_20_2eye_0_75 = warn_info.eye2_2_0f75 =
                    GetEyeCloseAddUpTime(eye_60s_result, 2, 600);
                medium_eye_close_continue = warn_info.eye2_20s_1f5 =
                    GetEyeCloseAddUpTime(eye_60s_result, 1, 1200);
            }

            if (histor_warn_type < DROWSINESS_LEVEL_MEDIUM ||
                (histor_warn_type == DROWSINESS_LEVEL_MEDIUM && his_warn_class != MOUTH_RELATED))
                medium_60_3mouth = warn_info.mouth2_120s_3 = GetMouthCount(mouth_result, 3);

            if (medium_60_10_eye || medium_60_3mouth || medium_20_2eye_0_75 ||
                medium_eye_close_continue)
                status = DROWSINESS_LEVEL_MEDIUM;

            if (medium_60_10_eye)
                warning_type = "medium_60_10_eye";
            if (medium_60_3mouth)
                warning_type = "medium_60_3mouth";
            if (medium_20_2eye_0_75)
                warning_type = "medium_20_2eye_0_75";
            if (medium_eye_close_continue)
                warning_type = "medium_eye_close_continue";
        }
        if (status == NORMAL && histor_warn_type <= DROWSINESS_LEVEL_LIGHT) {
            if (histor_warn_type < DROWSINESS_LEVEL_LIGHT ||
                (histor_warn_type == DROWSINESS_LEVEL_LIGHT && his_warn_class != EYES_RELATED))
                light_60_9_eye = warn_info.eye1_60s_10p = GetEyeCloseThreshold(0.13);

            if (histor_warn_type < DROWSINESS_LEVEL_LIGHT ||
                (histor_warn_type == DROWSINESS_LEVEL_LIGHT && his_warn_class != MOUTH_RELATED))
                light_60_2mouth = warn_info.mouth1_120s_2 = GetMouthCount(mouth_result, 2);

            if (light_60_9_eye || light_60_2mouth)
                status = DROWSINESS_LEVEL_LIGHT;

            if (light_60_9_eye)
                warning_type = "light_60_9_eye";
            if (light_60_2mouth)
                warning_type = "light_60_2mouth";
        }

        WarningClass warn_class = CurWarnType(warning_type);
        // std::cout << "1 warning_type:" << warning_type << " status:" << status
        //     << " histor_warn_type:" << histor_warn_type << " last_warning_type:" << last_warning_type << " warn_count:" << warn_count << std::endl;

        if (histor_warn_type < status) {
            histor_warn_type = status;  //最新的报警等级，存在历史结果
            his_warn_class = warn_class;
            alarm_start_time = cache_time;  //升级后,更新报警开始时间
            warn_count = 0;                 //升级重置报警帧数
        } else {
            if (warn_class == his_warn_class && warning_type != last_warning_type &&
                (warn_count <= 0 || warn_count >= 5))  // 同类型同等级且不在报警持续时间中不能输出
            {
                status = NORMAL;
            } else
                status = histor_warn_type;  //持续报上次结果

            // if (histor_warn_type == status) // 修复平级报警时候，多报的问题
            //     his_warn_class = warn_class;
        }

        //判断降级（60s没有一个有效哈欠2s或者没有一个有效闭眼0.6s）视为触发降级，即为正常状态 ->
        // 更改为在进入任意疲劳等级状态后，60S退出当前疲劳等级状态，进入正常状态，清零各时间窗累计，重新开始识别累计
        alarm_end_time = cache_time;
        if (status != NORMAL && getSec(alarm_start_time, alarm_end_time) >= 60) {
            Reset();
            status = NORMAL;
        }

        //每次报警只报5帧
        if (status != NORMAL && warn_count < 5) {
            warn_count++;
        } else if (status != NORMAL && !warning_type.empty() && warning_type != last_warning_type) {
            warn_count = 1;
        } else {
            status = NORMAL;
        }

        //本次报警类型跟上次不同，更新
        if (last_warning_type != warning_type && !warning_type.empty())
            last_warning_type = warning_type;

        // std::cout << "2 warning_type:" << warning_type << " last_warning_type:" << last_warning_type << " status:" << status << std::endl;
        // 打印fatigue log
        struct timeval tv;
        gettimeofday(&tv, NULL);
        long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
        if (now_ts - last_ts >= 1000) {
            std::cout << "[DMS Fatigue]:" << status << "||" << tired_info_.total_eye_count << "|"
                      << tired_info_.close_eye_ratio << "|" << tired_info_.close_eye_count << " "
                      << tired_info_.close_eye_duration_ms << " "
                      << tired_info_.close_eye_start_end_index << "|"
                      << tired_info_.total_mouth_count << "|" << tired_info_.yawn_count << " "
                      << tired_info_.open_mouth_duration_ms << " "
                      << tired_info_.open_mouth_start_end_index << std::endl;
            last_ts = now_ts;
        }
    } while (false);

    return status;
}

void DrowsinessWarn::SetOk() {
    ok_flag = true;
}

void DrowsinessWarn::GetTired(tx_tired& tired_info) {
    // 闭眼眨眼相关信息
    tired_info.total_eye_count = tired_info_.total_eye_count;
    tired_info.close_eye_ratio = tired_info_.close_eye_ratio;

    tired_info.close_eye_count = tired_info_.close_eye_count;
    tired_info.close_eye_duration_ms = tired_info_.close_eye_duration_ms;
    tired_info.close_eye_start_end_index = tired_info_.close_eye_start_end_index;

    // 哈欠相关信息
    tired_info.total_mouth_count = tired_info_.total_mouth_count;
    tired_info.yawn_count = tired_info_.yawn_count;
    tired_info.open_mouth_duration_ms = tired_info_.open_mouth_duration_ms;
    tired_info.open_mouth_start_end_index = tired_info_.open_mouth_start_end_index;

    return;
}

}  // namespace tongxing
