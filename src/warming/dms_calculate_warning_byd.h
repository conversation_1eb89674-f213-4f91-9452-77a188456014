#ifndef _DMS_CALCULATE_WARNING_BYD_H_
#define _DMS_CALCULATE_WARNING_BYD_H_
#include <algorithm>
#include <chrono>
#include <deque>
#include <iostream>
#include <vector>
#include "tx_dms_sdk.h"
#include "tx_dms_warning.h"
namespace tongxing {
// 此类使用与ddaw报警逻辑

// DMS报警类型
typedef enum WarningType_ {
    NORMAL = 0,                   //正常
    DROWSINESS_LEVEL_LIGHT = 2,   //轻度疲劳
    DROWSINESS_LEVEL_MEDIUM = 3,  //中度疲劳
    DROWSINESS_LEVEL_HEAVEY = 4,  //重度疲劳
    NO_RESPONSE = 5,              //无响应状态

} WarningType;

typedef enum WarningClass_ {
    NONE_RELATED = 0,   //无相关
    EYES_RELATED = 1,   //眼睛相关
    MOUTH_RELATED = 2,  //张嘴相关
} WarningClass;

// 报警类型信息结构体
typedef struct Warn_Info_ {
    // 闭眼状态
    bool eye_close_status;
    // 张嘴状态
    size_t mouth_open_status;
    // ldw状态
    bool ldw_status;
    //输入时间点
    long time_input;

} Warn_Info;

class DrowsinessWarn final {
  public:
    DrowsinessWarn() = default;
    DrowsinessWarn(int car_speed, int eye_timewindow, int mouth_timewindow, int ldw_timewindow);
    ~DrowsinessWarn();

    //更新报警信息
    void Update(const Warn_Info& info);

    //获取报警类型
    WarningType GetWarnStatus(TXWarnInfo& warn_info);

    //重置所有报警
    void Reset();

    //收到确认OK信号
    void SetOk();

    //获取闭眼张嘴信息
    void GetTired(tx_tired& tired_info);

    // 清除闭眼信息
    void Cleareye();

  private:
    int car_speed_ = 70;          //报警生效车速
    int eye_timewindow_ = 60;     //闭眼滑动时间窗（最大）
    int mouth_timewindow_ = 120;  //张嘴滑动时间窗（最大）
    int ldw_timewindow_ = 120;    //ldw滑动时间窗（最大）

    bool time_2min_flag = false;  //2分钟时间窗标准位
    long time_2min_start = 0;
    long time_2min_end = 0;

    bool time_2min_arrived = false;

    bool ok_flag = false;
    WarningType histor_warn_type = NORMAL;  //历史结果
    WarningClass his_warn_class = NONE_RELATED;

    tx_tired tired_info_;

    //报警计时时间
    long alarm_start_time = 0;
    long alarm_end_time = 0;

    long alarm_ok_start_time = 0;
    long alarm_ok_end_time = 0;

    //缓存时间点
    long cache_time;
    long last_ts = 0;
    int warn_count = 0;                  //报警帧数
    std::string last_warning_type = "";  //历史报警类型

    std::deque<std::pair<long, size_t>> mouth_result;  //嘴巴状态数据队列

    std::deque<std::pair<long, bool>> eye_60s_result;    //60s眼睛状态数据队列
    bool time_60s_arrived = false;
    bool time_60s_flag = false;  //60s时间窗标准位
    long time_60s_start = 0;
    long time_60s_end = 0;

    bool warn_no_response_flag = false;

    Warn_Info temp_warn;  //临时存放报警数据
    std::deque<std::pair<long, bool>> eye_temp_result;
    std::deque<std::pair<long, bool>> mouth_temp_result;
    bool history_flag = false;  //标记是否为历史结果
    float percentage = 0.0;     //内部测试使用

    // 获取时间秒
    long getSec(const long begin, const long end) { return (end - begin) * 1.0 / 1000; }

    bool GetEyeCloseThreshold(double thres);  //根据阈值占比计算

    bool GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                              const int& count,
                              long interval_start_ms,
                              long interval_end_ms);

    bool GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                              const int& count,
                              long interval_start_ms);

    bool GetEyeCloseAddUpTime(std::deque<std::pair<long, bool>>& eye_deque,
                              long interval_start_ms,
                              long interval_end_ms);

    bool GetMouthCount(std::deque<std::pair<long, size_t>>& mouth_deque, const int& count);
};

}  // namespace tongxing
#endif