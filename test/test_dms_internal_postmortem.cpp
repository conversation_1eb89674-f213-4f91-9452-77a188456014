/*
    事后分析原因
*/
#include <stddef.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include "json.h"
#include <csignal>

#include "cc_media_server.h"
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"
#include "calmcar_dms_process.h"

// read file name
#include <dirent.h>
#include <iostream>
#include <fstream>
#include <string>
#include <cstring>
#include <sys/stat.h>

long last_ts = 0;
TXDmsResult dms_result;
bool run_flag =true;

tongxing::CcDmsProcess handle;

static long get_timestamp() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    return now_ts;
}

static std::string extractDirectoryName(const std::string& filePath) {
    std::istringstream pathStream(filePath);
    std::string item;
    std::vector<std::string> pathParts;

    while (std::getline(pathStream, item, '/')) {
        if (!item.empty()) {
            pathParts.push_back(item);
        }
    }

    if (pathParts.size() >= 2) {
        return pathParts[pathParts.size() - 2];
    } else {
        return "";
    }
}
static int InitSdk() {
    std::cout << "dms verson: " << TXDmsGetVersion() << std::endl;
    std::cout << "dms really verson: " << TXDmsGetRealVersion() << std::endl;
    // hDms = TXDmsCreate(NULL, "cache/");
    int iRet = handle.Init(NULL, NULL);
    if (iRet != 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }

    // TXPoint2i left_top_point;
    // TXPoint2i right_bottom_point;
    // left_top_point.x = 280;
    // left_top_point.y = 0;
    // right_bottom_point.x = 1000;
    // right_bottom_point.y = 720;
    // TXDmsSetDriverRoi(hDms, &left_top_point, &right_bottom_point);
    return 0;
}

static void RunSdk(std::string image_path, TXCarInfo& carInfo, long ts) {
    // cv::Mat image = cv::imread(root["image"].asString(), 0);  //读取图片;
    cv::Mat image = cv::imread(image_path, 0);

    TXImageInfo image_;
    // cv::resize(image, image, cv::Size(1280, 720));
    image_.dataType = TXInputFormat::GRAY;
    image_.height = image.size().height;
    image_.width = image.size().width;
    image_.stride = image.size().width;
    image_.dataLen = image_.height * image_.width;
    image_.data = (char*)image.data;

    carInfo.speed = 80;//car_speed;
    carInfo.gear = TXGearPition::FORWARD;
    carInfo.steer_whl_snsr_rad = 0;
    carInfo.turn_light = TXTurnSignal::TURN_OFF;
    carInfo.driver_door_status = TXDriverDoorStatus::DOOR_CLOSE;
    carInfo.driver_seat_status = TXDriverSeatStatus::SEAT_STATIC;

    carInfo.mask =
        (TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_STEER_WHL_SNSR | TX_CAR_INFO_MASK_GEARPOSITION |
         TX_CAR_INFO_MASK_TURN_LIGHT | TX_CAR_DRIVER_DOOR | TX_CAR_DRIVER_SEAT);

    handle.updateCarInfo(&carInfo);
    double start_ts = get_timestamp();
    handle.SetInput(&image_, &dms_result, ts);
    double end_ts = get_timestamp();
    printf("TXDmsSetInput ts:%f\n", (float)(end_ts - start_ts));
}
void read_files_(std::string path, std::vector<std::string> suffix, std::vector<std::string> &files)
{
    DIR           *dir;
    struct dirent *ent;
    std::cout << "path:" << path << std::endl;
    if ((dir = opendir(path.c_str())) != NULL)
    {
        while ((ent = readdir(dir)) != NULL)
        {
            std::string fileName = ent->d_name;

            auto pos = fileName.rfind('.');
            if (pos != std::string::npos)
            {
                std::string ext = fileName.substr(pos);
                for (const auto &e : suffix)
                {
                    if (ext == e)
                    {
                        files.push_back(fileName);
                        continue;
                    }
                }
            }
        }
        closedir(dir);
        // 对files按文件名排序
        std::sort(files.begin(), files.end(), [](const std::string &a, const std::string &b) {
            // 将文件名转换为数字进行比较
            return std::stoi(a) < std::stoi(b);
        });
    }
    else
    {
        perror("read path error");
    }
}

static void SaveResultToJsonFile(std::string& str, long ts, TXCarInfo carInfo) {
    Json::Value root;
    Json::Value dms_result_json;
    Json::Value warnInfo_json;
    Json::Value tired_json;
    Json::Value distraction_json;
    dms_result_json["result_frame_id"] = dms_result.result_frame_id;
    dms_result_json["camera_status"] = dms_result.camera_status;
    dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
    dms_result_json["distraction_type"] = dms_result.distraction_status;
    dms_result_json["calibrate_status"] = dms_result.calibrate_status;
    dms_result_json["system_status"] = dms_result.system_status;
    dms_result_json["car_speed"] = carInfo.speed;
    dms_result_json["car_gear"] = carInfo.gear;
    dms_result_json["car_steer_whl_snsr_rad"] = carInfo.steer_whl_snsr_rad;
    dms_result_json["turn_light"] = carInfo.turn_light;
    dms_result_json["door_status"] = carInfo.driver_door_status;
    dms_result_json["seat_status"] = carInfo.driver_seat_status;
    dms_result_json["distraction_params"] = handle.GetDistractParamers();
    dms_result_json["distraction_reason"] = handle.GetDistractReason(dms_result.camera_status);
    float left_eye_thr, right_eye_thr;
    handle.GetRightLeftEyeThr(left_eye_thr, right_eye_thr);
    dms_result_json["left_up_down_proportion"] = left_eye_thr;
    dms_result_json["right_up_down_proportion"] = right_eye_thr;
    {
        Json::Value face_info;
        face_info["score"] = dms_result.face_info.score;
        face_info["xmin"] = dms_result.face_info.xmin / 2;
        face_info["ymin"] = dms_result.face_info.ymin / 2;
        face_info["xmax"] = dms_result.face_info.xmax / 2;
        face_info["ymax"] = dms_result.face_info.ymax / 2;
        face_info["yaw"] = dms_result.face_info.head_yaw;
        face_info["pitch"] = dms_result.face_info.head_pitch;
        face_info["roll"] = dms_result.face_info.head_roll;
        face_info["isMask"] = dms_result.face_info.isMask;
        face_info["isGlass"] = dms_result.face_info.isGlass;
        face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
        face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
        face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
        face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
        Json::Value landmarks(Json::arrayValue);
        for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
            Json::Value point;
            point["x"] = dms_result.face_info.landmarks[i].x / 2;
            point["y"] = dms_result.face_info.landmarks[i].y / 2;
            landmarks[i] = point;
        }

        {
            Json::Value right_eye_landmark;
            right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
            right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
            right_eye_landmark["eye_center"] = Json::Value();
            right_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.right_eye_landmark.eye_center.x / 2;
            right_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.right_eye_landmark.eye_center.y / 2;
            right_eye_landmark["eye_size"] = Json::Value();
            right_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.right_eye_landmark.eye_size.width / 2;
            right_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.right_eye_landmark.eye_size.height / 2;
            right_eye_landmark["iris_score"] = dms_result.face_info.right_eye_landmark.iris_score;
            right_eye_landmark["iris_center"] = Json::Value();
            right_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.right_eye_landmark.iris_center.x / 2;
            right_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.right_eye_landmark.iris_center.y / 2;
            right_eye_landmark["iris_radius"] =
                dms_result.face_info.right_eye_landmark.iris_radius / 2;
            right_eye_landmark["pupil_score"] = dms_result.face_info.right_eye_landmark.pupil_score;
            right_eye_landmark["pupil_center"] = Json::Value();
            right_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.right_eye_landmark.pupil_center.x / 2;
            right_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.right_eye_landmark.pupil_center.y / 2;
            right_eye_landmark["pupil_radius"] =
                dms_result.face_info.right_eye_landmark.pupil_radius / 2;
            right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
            right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
            right_eye_landmark["opening"] = dms_result.face_info.right_eye_landmark.opening;
            
            tongxing::CcDmsProcess::EyeInfos eyeinfos = {0};
            handle.get_eye_info_(eyeinfos);
            
            right_eye_landmark["true_eye_score"] = eyeinfos.right_eye.true_eye_score;
            right_eye_landmark["eye_coutours"] = Json::Value();
            Json::Value reye_coutours(Json::arrayValue);
            for (int i = 0; i < 8; i++) {
                Json::Value point;
                point["x"] = eyeinfos.right_eye.eye_coutours[i].x / 2;
                point["y"] = eyeinfos.right_eye.eye_coutours[i].y / 2;
                reye_coutours[i] = point;
            }
            right_eye_landmark["eye_coutours"] = reye_coutours;

            right_eye_landmark["true_iris_score"] = eyeinfos.right_eye.true_iris_score;
            right_eye_landmark["iris_coutours"] = Json::Value();
            Json::Value riris_coutours(Json::arrayValue);
            for (int i = 0; i < 8; i++) {
                Json::Value point;
                point["x"] = eyeinfos.right_eye.iris_coutours[i].x / 2;
                point["y"] = eyeinfos.right_eye.iris_coutours[i].y / 2;
                riris_coutours[i] = point;
            }
            right_eye_landmark["iris_coutours"] = riris_coutours;

            right_eye_landmark["true_pupil_score"] = eyeinfos.right_eye.true_pupil_score;
            right_eye_landmark["pupil"] = Json::Value();
            right_eye_landmark["pupil"]["x"] = eyeinfos.right_eye.pupil.x / 2;
            right_eye_landmark["pupil"]["y"] = eyeinfos.right_eye.pupil.y / 2;
            right_eye_landmark["curve_score"] = eyeinfos.right_eye.curve_score;
            right_eye_landmark["is_curve"] = eyeinfos.right_eye.is_curve;
            face_info["right_eye_landmark"] = right_eye_landmark;
            Json::Value left_eye_landmark;
            left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
            left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
            left_eye_landmark["eye_center"] = Json::Value();
            left_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.left_eye_landmark.eye_center.x / 2;
            left_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.left_eye_landmark.eye_center.y / 2;
            left_eye_landmark["eye_size"] = Json::Value();
            left_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.left_eye_landmark.eye_size.width / 2;
            left_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.left_eye_landmark.eye_size.height / 2;
            left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
            left_eye_landmark["iris_center"] = Json::Value();
            left_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.left_eye_landmark.iris_center.x / 2;
            left_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.left_eye_landmark.iris_center.y / 2;
            left_eye_landmark["iris_radius"] =
                dms_result.face_info.left_eye_landmark.iris_radius / 2;
            left_eye_landmark["pupil_score"] = dms_result.face_info.left_eye_landmark.pupil_score;
            left_eye_landmark["pupil_center"] = Json::Value();
            left_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.left_eye_landmark.pupil_center.x / 2;
            left_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.left_eye_landmark.pupil_center.y / 2;
            left_eye_landmark["pupil_radius"] =
                dms_result.face_info.left_eye_landmark.pupil_radius / 2;
            left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
            left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
            left_eye_landmark["opening"] = dms_result.face_info.left_eye_landmark.opening;
            left_eye_landmark["true_eye_score"] = eyeinfos.left_eye.true_eye_score;
            left_eye_landmark["eye_coutours"] = Json::Value();
            Json::Value leye_coutours(Json::arrayValue);
            for (int i = 0; i < 8; i++) {
                Json::Value point;
                point["x"] = eyeinfos.left_eye.eye_coutours[i].x / 2;
                point["y"] = eyeinfos.left_eye.eye_coutours[i].y / 2;
                leye_coutours[i] = point;
            }
            left_eye_landmark["eye_coutours"] = leye_coutours;

            left_eye_landmark["true_iris_score"] = eyeinfos.left_eye.true_iris_score;
            left_eye_landmark["iris_coutours"] = Json::Value();
            Json::Value liris_coutours(Json::arrayValue);
            for (int i = 0; i < 8; i++) {
                Json::Value point;
                point["x"] = eyeinfos.left_eye.iris_coutours[i].x / 2;
                point["y"] = eyeinfos.left_eye.iris_coutours[i].y / 2;
                liris_coutours[i] = point;
            }
            left_eye_landmark["iris_coutours"] = liris_coutours;

            left_eye_landmark["true_pupil_score"] = eyeinfos.left_eye.true_pupil_score;
            left_eye_landmark["pupil"] = Json::Value();
            left_eye_landmark["pupil"]["x"] = eyeinfos.left_eye.pupil.x / 2;
            left_eye_landmark["pupil"]["y"] = eyeinfos.left_eye.pupil.y / 2;
            left_eye_landmark["curve_score"] = eyeinfos.left_eye.curve_score;
            left_eye_landmark["is_curve"] = eyeinfos.left_eye.is_curve;
            face_info["left_eye_landmark"] = left_eye_landmark;
        }
        face_info["landmarks"] = landmarks;
        dms_result_json["face_info"] = face_info;
    }
    {
        warnInfo_json["eye1_60s_10p"] = dms_result.face_info.warnInfo.eye1_60s_10p;
        warnInfo_json["mouth1_120s_2"] = dms_result.face_info.warnInfo.mouth1_120s_2;
        warnInfo_json["mouth1_1l_1"] = dms_result.face_info.warnInfo.mouth1_1l_1;
        warnInfo_json["eye2_20s_1f5"] = dms_result.face_info.warnInfo.eye2_20s_1f5;
        warnInfo_json["eye2_2_0f75"] = dms_result.face_info.warnInfo.eye2_2_0f75;
        warnInfo_json["eye2_1l_0f75"] = dms_result.face_info.warnInfo.eye2_1l_0f75;
        warnInfo_json["eye2_60s_12p"] = dms_result.face_info.warnInfo.eye2_60s_12p;
        warnInfo_json["mouth2_1l_2"] = dms_result.face_info.warnInfo.mouth2_1l_2;
        warnInfo_json["mouth2_120s_3"] = dms_result.face_info.warnInfo.mouth2_120s_3;
        warnInfo_json["eye3_20s_2f4"] = dms_result.face_info.warnInfo.eye3_20s_2f4;
        warnInfo_json["eye3_2_1f2"] = dms_result.face_info.warnInfo.eye3_2_1f2;
        warnInfo_json["eye3_1l_1f2"] = dms_result.face_info.warnInfo.eye3_1l_1f2;
        warnInfo_json["repetition"] = dms_result.face_info.warnInfo.repetition;
    }
    {
        tx_tired temp = {0};
        handle.GetTiredInfo(temp);
        tired_json["total_eye_count"] = temp.total_eye_count;
        tired_json["close_eye_ratio"] = temp.close_eye_ratio;
        tired_json["close_eye_count"] = int(temp.close_eye_count);
        tired_json["close_eye_duration_ms"] = temp.close_eye_duration_ms;
        tired_json["close_eye_start_end_index"] = (temp.close_eye_start_end_index);
        tired_json["total_mouth_count"] = int(temp.total_mouth_count);
        tired_json["yawn_count"] = temp.yawn_count;
        tired_json["open_mouth_duration_ms"] = (temp.open_mouth_duration_ms);
        tired_json["open_mouth_start_end_index"] = (temp.open_mouth_start_end_index);
    }
    {
        internal_analysis_distraction_info temp = {0};
        handle.GetDistractionInfo(temp);
        distraction_json["distraction_continue_percent"] = temp.distraction_continue_percent;
        distraction_json["distraction_continue_time"] = int(temp.distraction_continue_time);
        distraction_json["distraction_sum_time"] = int(temp.distraction_sum_time);
        distraction_json["distraction_front_continue_time"] =
            int(temp.distraction_front_continue_time);
        distraction_json["time_gap"] = int(temp.time_gap);
    }
    root["ts"] = int(ts);
    root["dms_result"] = dms_result_json;
    root["warnInfo"] = warnInfo_json;
    root["tiredInfo"] = tired_json;
    root["distractionInfo"] = distraction_json;
    root["sdk_version"] = TXDmsGetVersion();
    root["sdk_really_version"] = handle.GetBuildType() + std::string("_") + TXDmsGetRealVersion();

    //写入json
    Json::StyledWriter writer;
    std::ofstream os;
    std::string json_path_root = "./output_json/"+ extractDirectoryName(str) + "/";
    // std::cout << "json_path_root:"<< json_path_root << std::endl;
    if ((access("./output_json", 0)) != -1) {
    } else {
        mkdir("./output_json", S_IRWXU);
    }
    if ((access(json_path_root.c_str(), 0)) != -1) {
    } else {
        mkdir(json_path_root.c_str(), S_IRWXU);
    }
    std::string::size_type at_start = str.find_last_of("/");
    std::string::size_type at_end = str.find_last_of(".");
    std::string json_path = str.substr(at_start, at_end - at_start);
    std::string json_output_file = json_path_root + json_path + ".json";

    os.open(json_output_file);
    os << writer.write(root);
    os.close();
}

struct FrameData {
    int frame_id;
    std::string file_path;
};

std::vector<FrameData> parse_frame_data(const std::string& file_path) {
    std::ifstream infile(file_path);  
    if (!infile.is_open()) {
        std::cerr << "Failed to open file: " << file_path << std::endl;
        return {}; 
    }

    std::string line;
    std::vector<FrameData> frames;  

    // 逐行读取文件
    while (std::getline(infile, line)) {
        std::stringstream ss(line);  
        std::string token;

        FrameData data;

        if (std::getline(ss, token, ',')) {
            data.frame_id = std::stoi(token);  
        }

        if (std::getline(ss, token)) {
            data.file_path = token; 
        }

        frames.push_back(data);
    }

    infile.close();  

    return frames;  
}

static void trap(int signal)
{
    printf("------ force exit------------\n");
    run_flag = false;
}

int main(int argc, char** argv) {

    if (argc != 2) {
        printf("Usage %s  parseinfo.txt \n", argv[0]);
        return -1;
    }
    // 退出机制
    signal(SIGINT, &trap);
    signal(SIGTERM, &trap);

    std::string parseinfo_path = argv[1];

    if (InitSdk() != 0) {
        return -2;
    }
    usleep(100);
    printf("InitSdk success!\n");
    
    long last_frame_id = -1;
    long now_ts = 0;
    int useless_sleep_count = 0;
    struct timeval tv;
    gettimeofday(&tv, NULL);
    now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;
    auto start_time = std::chrono::high_resolution_clock::now();
    auto end_time = std::chrono::high_resolution_clock::now();
    do {
        std::vector<FrameData> frames = parse_frame_data(parseinfo_path);

        if (last_frame_id != -1) {
            // 间隔读取index_map,在外层会对index_map进行删除和添加
            // std::cout << "sleep 0.5s wait image generate..." << std::endl;
            // sleep(0.5);
            frames = parse_frame_data(parseinfo_path);
            if (frames.empty() || frames.size() == last_frame_id+1 && useless_sleep_count < 3) {
                // useless_sleep_count ++;
                std::cout << "algo sdk continue..." << std::endl;
                sleep(2);
                continue;
            }
            // if (useless_sleep_count >= 3) {
            //     std::cout << "ending algo sdk..." << std::endl;
            //     end_time = std::chrono::high_resolution_clock::now();
            //     std::chrono::duration<double> elapsed_time = (end_time - start_time);
            //     std::cout << "algo sdk elapsed_time:"<< elapsed_time.count() << "s." << std::endl;
            //     return -1;
            // }

        }
        // std::cout << "frames.size():" << frames.size() << std::endl;
        int start_frame_id = last_frame_id+1;
        for (size_t i = start_frame_id; i < frames.size(); i++) {
        // for (auto &frame : frames) {
            // 正确性校验
            if (frames[i].frame_id != last_frame_id + 1) 
                std::cout << "WARN: frames[i] id "<< frames[i].frame_id <<  "!=last_frame_id " << last_frame_id <<" is not correct... "  << std::endl;
                
            //运行算法
            long ts = now_ts+frames[i].frame_id*100; //模拟成10fps
            if (frames[i].frame_id % 1 == 0)
                std::cout << "[" << frames[i].frame_id << ", "<<ts<<"]:" << frames[i].file_path << std::endl;

            TXCarInfo carInfo = {0};
            RunSdk(frames[i].file_path, carInfo, ts);
            SaveResultToJsonFile(frames[i].file_path, ts, carInfo);
            last_frame_id = frames[i].frame_id;
            // usleep(1); //防止cpu的高占用率
        }

    }while(run_flag);

    end_time = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double> elapsed_time = (end_time - start_time);
    std::cout << "algo sdk elapsed_time:"<< elapsed_time.count() << "s." << std::endl;
    std::cout << "ending algo sdk..." << std::endl;

    return 0;
}