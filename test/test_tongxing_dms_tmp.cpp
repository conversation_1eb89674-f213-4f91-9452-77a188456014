#include <stddef.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>
#include <iostream>
#include "calmcar_dms_process.h"
#include "cc_media_server.h"
#include "json.h"
#include "opencv2/opencv.hpp"
#include "tx_dms_sdk.h"

// read file name
#include <dirent.h>
#include <sys/stat.h>
#include <cstring>
#include <fstream>
#include <iostream>
#include <string>

long last_ts = 0;
TXDmsResult dms_result;

tongxing::CcDmsProcess handle;

static std::string extractDirectoryName(const std::string& filePath) {
    std::istringstream pathStream(filePath);
    std::string item;
    std::vector<std::string> pathParts;

    while (std::getline(pathStream, item, '/')) {
        if (!item.empty()) {
            pathParts.push_back(item);
        }
    }

    if (pathParts.size() >= 2) {
        return pathParts[pathParts.size() - 2];
    } else {
        return "";
    }
}

void read_files_(std::string path,
                 std::vector<std::string> suffix,
                 std::vector<std::string>& files) {
    DIR* dir;
    struct dirent* ent;
    std::cout << "path:" << path << std::endl;
    if ((dir = opendir(path.c_str())) != NULL) {
        while ((ent = readdir(dir)) != NULL) {
            std::string fileName = ent->d_name;

            auto pos = fileName.rfind('.');
            if (pos != std::string::npos) {
                std::string ext = fileName.substr(pos);
                for (const auto& e : suffix) {
                    if (ext == e) {
                        files.push_back(fileName);
                        continue;
                    }
                }
            }
        }
        closedir(dir);
        // 对files按文件名排序
        std::sort(files.begin(), files.end(), [](const std::string& a, const std::string& b) {
            // 自然排序比较函数
            auto ai = a.begin(), bi = b.begin();
            while (ai != a.end() && bi != b.end()) {
                if (isdigit(*ai) && isdigit(*bi)) {
                    // 处理数字部分
                    char* end_a;
                    char* end_b;
                    long na = strtol(&(*ai), &end_a, 10);
                    long nb = strtol(&(*bi), &end_b, 10);
                    if (na != nb)
                        return na < nb;
                    ai += (end_a - &(*ai));
                    bi += (end_b - &(*bi));
                } else {
                    // 处理非数字部分
                    if (*ai != *bi)
                        return *ai < *bi;
                    ++ai;
                    ++bi;
                }
            }
            return a.length() < b.length();
        });
    } else {
        perror("read path error");
    }
}

static void SaveResultToJsonFile(std::string& str, TXCarInfo carInfo) {
    Json::Value root;
    Json::Value dms_result_json;
    Json::Value warnInfo_json;
    dms_result_json["result_frame_id"] = dms_result.result_frame_id;
    dms_result_json["camera_status"] = dms_result.camera_status;
    dms_result_json["drowsiness_type"] = dms_result.drowsiness_status;
    dms_result_json["distraction_type"] = dms_result.distraction_status;
    dms_result_json["calibrate_status"] = dms_result.calibrate_status;
    dms_result_json["system_status"] = dms_result.system_status;
    {
        Json::Value face_info;
        face_info["score"] = dms_result.face_info.score;
        face_info["xmin"] = dms_result.face_info.xmin / 2;
        face_info["ymin"] = dms_result.face_info.ymin / 2;
        face_info["xmax"] = dms_result.face_info.xmax / 2;
        face_info["ymax"] = dms_result.face_info.ymax / 2;
        face_info["yaw"] = dms_result.face_info.head_yaw;
        face_info["pitch"] = dms_result.face_info.head_pitch;
        face_info["roll"] = dms_result.face_info.head_roll;
        face_info["isMask"] = dms_result.face_info.isMask;
        face_info["isGlass"] = dms_result.face_info.isGlass;
        face_info["isIRBlock"] = dms_result.face_info.isIRBlock;
        face_info["right_close_eye_score"] = dms_result.face_info.right_close_eye_score;
        face_info["left_close_eye_score"] = dms_result.face_info.left_close_eye_score;
        face_info["mouth_opening"] = dms_result.face_info.mouth_opening;
        Json::Value landmarks(Json::arrayValue);
        for (int i = 0; i < TX_MAX_FLD_SIZE; i++) {
            Json::Value point;
            point["x"] = dms_result.face_info.landmarks[i].x / 2;
            point["y"] = dms_result.face_info.landmarks[i].y / 2;
            landmarks[i] = point;
        }

        {
            Json::Value right_eye_landmark;
            right_eye_landmark["eye_score"] = dms_result.face_info.right_eye_landmark.eye_score;
            right_eye_landmark["eye_angle"] = dms_result.face_info.right_eye_landmark.eye_angle;
            right_eye_landmark["eye_center"] = Json::Value();
            right_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.right_eye_landmark.eye_center.x / 2;
            right_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.right_eye_landmark.eye_center.y / 2;
            right_eye_landmark["eye_size"] = Json::Value();
            right_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.right_eye_landmark.eye_size.width / 2;
            right_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.right_eye_landmark.eye_size.height / 2;
            right_eye_landmark["iris_score"] = dms_result.face_info.right_eye_landmark.iris_score;
            right_eye_landmark["iris_center"] = Json::Value();
            right_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.right_eye_landmark.iris_center.x / 2;
            right_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.right_eye_landmark.iris_center.y / 2;
            right_eye_landmark["iris_radius"] =
                dms_result.face_info.right_eye_landmark.iris_radius / 2;
            right_eye_landmark["pupil_score"] = dms_result.face_info.right_eye_landmark.pupil_score;
            right_eye_landmark["pupil_center"] = Json::Value();
            right_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.right_eye_landmark.pupil_center.x / 2;
            right_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.right_eye_landmark.pupil_center.y / 2;
            right_eye_landmark["pupil_radius"] =
                dms_result.face_info.right_eye_landmark.pupil_radius / 2;
            right_eye_landmark["yaw"] = dms_result.face_info.right_eye_landmark.yaw;
            right_eye_landmark["pitch"] = dms_result.face_info.right_eye_landmark.pitch;
            face_info["right_eye_landmark"] = right_eye_landmark;
            Json::Value left_eye_landmark;
            left_eye_landmark["eye_score"] = dms_result.face_info.left_eye_landmark.eye_score;
            left_eye_landmark["eye_angle"] = dms_result.face_info.left_eye_landmark.eye_angle;
            left_eye_landmark["eye_center"] = Json::Value();
            left_eye_landmark["eye_center"]["x"] =
                dms_result.face_info.left_eye_landmark.eye_center.x / 2;
            left_eye_landmark["eye_center"]["y"] =
                dms_result.face_info.left_eye_landmark.eye_center.y / 2;
            left_eye_landmark["eye_size"] = Json::Value();
            left_eye_landmark["eye_size"]["width"] =
                dms_result.face_info.left_eye_landmark.eye_size.width / 2;
            left_eye_landmark["eye_size"]["height"] =
                dms_result.face_info.left_eye_landmark.eye_size.height / 2;
            left_eye_landmark["iris_score"] = dms_result.face_info.left_eye_landmark.iris_score;
            left_eye_landmark["iris_center"] = Json::Value();
            left_eye_landmark["iris_center"]["x"] =
                dms_result.face_info.left_eye_landmark.iris_center.x / 2;
            left_eye_landmark["iris_center"]["y"] =
                dms_result.face_info.left_eye_landmark.iris_center.y / 2;
            left_eye_landmark["iris_radius"] =
                dms_result.face_info.left_eye_landmark.iris_radius / 2;
            left_eye_landmark["pupil_score"] = dms_result.face_info.left_eye_landmark.pupil_score;
            left_eye_landmark["pupil_center"] = Json::Value();
            left_eye_landmark["pupil_center"]["x"] =
                dms_result.face_info.left_eye_landmark.pupil_center.x / 2;
            left_eye_landmark["pupil_center"]["y"] =
                dms_result.face_info.left_eye_landmark.pupil_center.y / 2;
            left_eye_landmark["pupil_radius"] =
                dms_result.face_info.left_eye_landmark.pupil_radius / 2;
            left_eye_landmark["yaw"] = dms_result.face_info.left_eye_landmark.yaw;
            left_eye_landmark["pitch"] = dms_result.face_info.left_eye_landmark.pitch;
            face_info["left_eye_landmark"] = left_eye_landmark;
        }
        face_info["landmarks"] = landmarks;
        dms_result_json["face_info"] = face_info;
    }
    {
        warnInfo_json["eye1_60s_10p"] = dms_result.face_info.warnInfo.eye1_60s_10p;
        warnInfo_json["mouth1_120s_2"] = dms_result.face_info.warnInfo.mouth1_120s_2;
        warnInfo_json["mouth1_1l_1"] = dms_result.face_info.warnInfo.mouth1_1l_1;
        warnInfo_json["eye2_20s_1f5"] = dms_result.face_info.warnInfo.eye2_20s_1f5;
        warnInfo_json["eye2_2_0f75"] = dms_result.face_info.warnInfo.eye2_2_0f75;
        warnInfo_json["eye2_1l_0f75"] = dms_result.face_info.warnInfo.eye2_1l_0f75;
        warnInfo_json["eye2_60s_12p"] = dms_result.face_info.warnInfo.eye2_60s_12p;
        warnInfo_json["mouth2_1l_2"] = dms_result.face_info.warnInfo.mouth2_1l_2;
        warnInfo_json["mouth2_120s_3"] = dms_result.face_info.warnInfo.mouth2_120s_3;
        warnInfo_json["eye3_20s_2f4"] = dms_result.face_info.warnInfo.eye3_20s_2f4;
        warnInfo_json["eye3_2_1f2"] = dms_result.face_info.warnInfo.eye3_2_1f2;
        warnInfo_json["eye3_1l_1f2"] = dms_result.face_info.warnInfo.eye3_1l_1f2;
        warnInfo_json["repetition"] = dms_result.face_info.warnInfo.repetition;
    }
    // root["ts"] = ts;
    root["dms_result"] = dms_result_json;
    root["warnInfo"] = warnInfo_json;
    root["sdk_version"] = TXDmsGetVersion();

    //写入json
    Json::StyledWriter writer;
    std::ofstream os;
    std::string json_path_root = "./output_json/" + extractDirectoryName(str) + "/";
    std::cout << "json_path_root:" << json_path_root << std::endl;

    if ((access(json_path_root.c_str(), 0)) != -1) {
    } else {
        mkdir(json_path_root.c_str(), S_IRWXU);
    }
    std::string::size_type at_start = str.find_last_of("/");
    std::string::size_type at_end = str.find_last_of(".");
    std::string json_path = str.substr(at_start, at_end - at_start);
    std::string json_output_file = json_path_root + json_path + "_out" + ".json";

    os.open(json_output_file);
    os << writer.write(root);
    os.close();
}

int main(int argc, char** argv) {
    last_ts = clock() / 1000;
    long img_index = 0;
    std::cout << "--------main------- start" << std::endl;
    std::cout << "dms verson: " << TXDmsGetVersion() << std::endl;
    std::cout << "dms really verson: "
              << handle.GetBuildType() + std::string("_") + TXDmsGetRealVersion() << std::endl;
    long hDms = TXDmsCreate(NULL, "cache/");
    if (hDms == 0) {
        std::cout << "creat dms fail" << std::endl;
        return -1;
    } else {
        std::cout << "creat dms success" << std::endl;
    }
    TXPoint2i left_top_point;
    TXPoint2i right_bottom_point;
    left_top_point.x = 280;
    left_top_point.y = 0;
    right_bottom_point.x = 1000;
    right_bottom_point.y = 720;
    TXDmsSetDriverRoi(hDms, &left_top_point, &right_bottom_point);
    cv::Mat yuv422M;
    TXDmsSetLogLevel(LEVEL_INFO);
    std::vector<std::string> suffix = {".png", ".jpg"};
    // std::string path = std::string(argv[1]);

    int dir_num = argc - 1;
    std::vector<std::string> dirlists;

    struct timeval tv;
    gettimeofday(&tv, NULL);
    long now_ts = tv.tv_sec * 1000 + tv.tv_usec / 1000;

    for (int i = 0; i < dir_num; ++i) {
        std::cout << "argv[i + 1]:" << argv[i + 1] << std::endl;
        dirlists.push_back(argv[i + 1]);
    }

    do {
        for (auto dir : dirlists) {
            // std::cout << "dir:" << dir << std::endl;
            std::string path = dir;
            std::vector<std::string> filelist;
            read_files_(path, suffix, filelist);

            for (auto& file : filelist) {
                std::cout << "frame id:" << dms_result.result_frame_id << " file:" << path + file
                          << std::endl;
                TXImageInfo image_;
                std::string file_name = path + file;
                cv::Mat image = cv::imread(file_name, 0);  //读取图片;
                cv::resize(image, image, cv::Size(1280, 720));
                image_.dataType = TXInputFormat::GRAY;
                image_.height = image.size().height;
                image_.width = image.size().width;
                image_.stride = image.size().width;
                image_.dataLen = image_.height * image_.width;
                image_.data = (char*)image.data;

                TXCarInfo carInfo = {0};

                TXDmsSetInput(hDms, &image_, &dms_result);
                carInfo.driver_seat_status = TXDriverSeatStatus::SEAT_STATIC;
                carInfo.mask = TX_CAR_INFO_MASK_SPEED | TX_CAR_INFO_MASK_TURN_LIGHT |
                    TX_CAR_INFO_MASK_GEARPOSITION | TX_CAR_DRIVER_SEAT;
                carInfo.speed = 80;
                carInfo.gear = TXGearPition::FORWARD;
                carInfo.turn_light = TXTurnSignal::TURN_OFF;

                TXDmsUpdataCarInfo(hDms, &carInfo);
                // printf("result_frame_id:%ld,camera_status:%d,drowsiness_status:%d,distraction_status:%d\n",
                //     dms_result.result_frame_id, dms_result.camera_status, dms_result.drowsiness_status,
                //     dms_result.distraction_status);
                // std::cout << "distrinterv_status:" << dms_result.distrinterv_status
                // << " distraction_status:" << dms_result.distraction_status << std::endl;
                // usleep(1);
                usleep(10000);

                // SaveResultToJsonFile(file_name, carInfo);
            }

            // // 是否继续按键询问
            // char ch;
            // std::cout << "Press a key: ";
            // std::cin >> ch;
            // std::cout << "You pressed: " << ch << "\n";
        }
        usleep(100);
    } while (1);
    // }while(0);

    TXDmsDestroy(hDms);

    return 0;
}